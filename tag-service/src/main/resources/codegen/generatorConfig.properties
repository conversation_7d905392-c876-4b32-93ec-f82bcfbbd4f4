# mysql jar
driver.class.path=C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/mysql/mysql-connector-java/5.1.47/9de4159aaf2d08817a276610b8114a825fca6cfd/mysql-connector-java-5.1.47.jar
#driver.class.path=C:/DEV/gradle-4.9/repository/caches/modules-2/files-2.1/mysql/mysql-connector-java/5.1.47/9de4159aaf2d08817a276610b8114a825fca6cfd/mysql-connector-java-5.1.47.jar
# jdbc connection config
jdbc.driver=com.mysql.jdbc.Driver
#jdbc.url=*****************************************************************************************************
jdbc.url=*****************************************************************************************************
jdbc.username=dev
jdbc.password=dev
# java model project and package
 model.package=com.ddmc.tag.model.base
 model.project=tag-service/src/main/java
# mapper.xml project and package
xml.package=com.ddmc.tag
xml.project=tag-service/src/main/resources
# dao.java project and package
mapper.package=com.ddmc.tag.dao.tag.rule
mapper.project=tag-service/src/main/java