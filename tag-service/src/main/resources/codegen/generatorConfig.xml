<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC
        "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd" >
<generatorConfiguration>

    <properties resource="codegen/generatorConfig.properties"/>

    <!-- !!!! Driver Class Path !!!! -->
    <classPathEntry location="${driver.class.path}"/>


    <!--<context id="context" targetRuntime="MyBatis3Simple">-->
    <context id="context" targetRuntime="com.demo.components.mybatis.codegen.CustomIntrospectedTableMyBatis3Impl">

        <property name="javaFileEncoding" value="UTF-8"/>

        <!--true时BaseResultMap和Base_Column_List仅仅包含NonBLOBsColumns-->
        <property name="isSimple" value="true"/>

        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <plugin type="com.demo.components.mybatis.codegen.plugins.JavaClientWrapperPlugin">
            <property name="javaClientTargetPackage" value="${mapper.package}"/>
            <property name="javaClientTargetProject" value="${mapper.project}"/>

            <property name="modelTargetPackage" value="${model.package}"/>
            <property name="modelTargetProject" value="${model.project}"/>
        </plugin>

        <!--为每个Dao生成一个扩展的sqlMap文件, 如UserMapperExt.xml-->
        <plugin type="com.demo.components.mybatis.codegen.plugins.MapperExtXmlGeneratorPlugin"/>

        <!--注释生成器-->
        <commentGenerator type="com.demo.components.mybatis.codegen.generator.CustomCommentGenerator">
            <property name="suppressDate" value="false"/>
            <property name="suppressAllComments" value="false"/><!--禁止注释-->
        </commentGenerator>

        <!-- !!!! Database Configurations !!!! -->
        <jdbcConnection driverClass="${jdbc.driver}"
                        connectionURL="${jdbc.url}"
                        userId="${jdbc.username}" password="${jdbc.password}"/>

        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <!-- !!!! Model Configurations !!!! -->
        <javaModelGenerator targetPackage="${model.package}" targetProject="${model.project}">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="false"/>
        </javaModelGenerator>

        <!-- !!!! Mapper XML Configurations !!!! -->
        <sqlMapGenerator targetPackage="${xml.package}" targetProject="${xml.project}">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <!-- !!!! Mapper Interface Configurations !!!! -->
        <javaClientGenerator targetPackage="${mapper.package}" targetProject="${mapper.project}"
                             type="XMLMAPPER">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <!--数据库表-->
        <!--<table tableName="synonyms"/>-->
        <table tableName="bigdate_product_lable"/>
    </context>
</generatorConfiguration>