<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.tag.dao.eslog.BigdateProductLableDao">
  <resultMap id="BaseResultMap" type="com.ddmc.tag.model.stats.BigdateProductLable">
    <!--@mbg.generated 2020-08-07 17:56:46-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="date" jdbcType="VARCHAR" property="date" />
    <result column="count" jdbcType="INTEGER" property="count" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated 2020-08-07 17:56:46-->
    id, date, count, update_time
  </sql>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ddmc.tag.model.stats.BigdateProductLable" useGeneratedKeys="true">
    <!--@mbg.generated 2020-08-07 17:56:46-->
    INSERT INTO bigdate_product_lable
    <trim prefix="(" suffix=")" suffixOverrides=",">
      date,
      <if test="count != null">
        count,
      </if>
      update_time,
    </trim>
    VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
      #{date,jdbcType=VARCHAR},
      <if test="count != null">
        #{count,jdbcType=INTEGER},
      </if>
      #{updateTime,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertList" parameterType="java.util.List">
    <!--@mbg.generated 2020-08-07 17:56:46-->
    INSERT INTO bigdate_product_lable (
      date, count, update_time
    ) VALUES 
    <foreach collection="list" index="index" item="item" separator=",">
      (
        #{item.date,jdbcType=VARCHAR}, #{item.count,jdbcType=INTEGER}, #{item.updateTime,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
  <select id="selectById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated 2020-08-07 17:56:46-->
    SELECT <include refid="Base_Column_List"/> FROM bigdate_product_lable WHERE id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectOne" parameterType="com.ddmc.tag.model.stats.BigdateProductLable" resultMap="BaseResultMap">
    <!--@mbg.generated 2020-08-07 17:56:46-->
    SELECT <include refid="Base_Column_List"/> FROM bigdate_product_lable
    <where>
      <if test="date != null">
        AND date = #{date,jdbcType=VARCHAR}
      </if>
      <if test="count != null">
        AND count = #{count,jdbcType=INTEGER}
      </if>
      <if test="updateTime != null">
        AND update_time = #{updateTime,jdbcType=VARCHAR}
      </if>
      LIMIT 1
    </where>
  </select>
  <select id="selectList" parameterType="com.ddmc.tag.model.stats.BigdateProductLable" resultMap="BaseResultMap">
    <!--@mbg.generated 2020-08-07 17:56:46-->
    SELECT <include refid="Base_Column_List"/> FROM bigdate_product_lable
    <where>
      <if test="date != null">
        AND date = #{date,jdbcType=VARCHAR}
      </if>
      <if test="count != null">
        AND count = #{count,jdbcType=INTEGER}
      </if>
      <if test="updateTime != null">
        AND update_time = #{updateTime,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    <!--@mbg.generated 2020-08-07 17:56:46-->
    SELECT <include refid="Base_Column_List"/> FROM bigdate_product_lable
  </select>
  <select id="count" parameterType="com.ddmc.tag.model.stats.BigdateProductLable" resultType="java.lang.Long">
    <!--@mbg.generated 2020-08-07 17:56:46-->
    SELECT count(1) FROM bigdate_product_lable
    <where>
      <if test="id != null">
        AND id=#{id,jdbcType=INTEGER}
      </if>
      <if test="date != null">
        AND date=#{date,jdbcType=VARCHAR}
      </if>
      <if test="count != null">
        AND count=#{count,jdbcType=INTEGER}
      </if>
      <if test="updateTime != null">
        AND update_time=#{updateTime,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
  <delete id="deleteById" parameterType="java.lang.Integer">
    <!--@mbg.generated 2020-08-07 17:56:46-->
    DELETE FROM bigdate_product_lable WHERE id = #{id,jdbcType=INTEGER}
  </delete>
  <update id="update" parameterType="com.ddmc.tag.model.stats.BigdateProductLable">
    <!--@mbg.generated 2020-08-07 17:56:46-->
    UPDATE bigdate_product_lable
    <set>
      <if test="date != null">
        date = #{date,jdbcType=VARCHAR},
      </if>
      <if test="count != null">
        count = #{count,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    WHERE id = #{id,jdbcType=INTEGER}
  </update>
</mapper>