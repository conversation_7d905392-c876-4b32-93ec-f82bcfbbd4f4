<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.tag.dao.eslog.BigdateProductStationLableDao">
    <select id="getProductTagCount" resultType="java.lang.Integer">
        select count(1)
        from bigdate_product_station_lable
        where date = date_format(now(),'%Y-%m-%d')
        order by id DESC LIMIT 1
    </select>
</mapper>