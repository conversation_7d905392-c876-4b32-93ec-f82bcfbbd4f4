<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.tag.dao.eslog.BigdataCountDao">
  <resultMap id="BaseResultMap" type="com.ddmc.tag.model.stats.BigdataCount">
    <id column="id" jdbcType="INTEGER" property="id" />
    <id column="type" jdbcType="TINYINT" property="type" />
    <result column="date" jdbcType="VARCHAR" property="date" />
    <result column="count" jdbcType="INTEGER" property="count" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, type, date, count, create_time, update_time
  </sql>

  <select id="getTagCount" parameterType="java.lang.Integer" resultType="java.lang.Integer">
    SELECT
      count(1)
    FROM
      bigdata_count
    WHERE
      type = #{type,jdbcType=TINYINT}
    AND
      date = date_format(now(),'%Y-%m-%d')
    ORDER BY id DESC LIMIT 1
  </select>

</mapper>