<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.tag.dao.eslog.UserProfileEsLogDao">
  <resultMap id="BaseResultMap" type="com.ddmc.tag.model.stats.UserProfileEsLog">
    <!--@mbg.generated 2020-04-26 17:33:51-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="date" jdbcType="VARCHAR" property="date" />
    <result column="count" jdbcType="INTEGER" property="count" />
    <result column="is_es_check_pass" jdbcType="INTEGER" property="isEsCheckPass" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, date, count, is_es_check_pass, update_time
  </sql>

  <select id="selectOne" parameterType="com.ddmc.tag.model.stats.UserProfileEsLog" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM bigdate_user_profile
    <where>
      <if test="date != null">
        AND date = #{date,jdbcType=VARCHAR}
      </if>
      ORDER BY id DESC
      LIMIT 1
    </where>
  </select>

  <update id="update" parameterType="com.ddmc.tag.model.stats.UserProfileEsLog">
    UPDATE bigdate_user_profile
    <set>
      <if test="date != null">
        date = #{date,jdbcType=VARCHAR},
      </if>
      <if test="count != null">
        count = #{count,jdbcType=INTEGER},
      </if>
      <if test="isEsCheckPass != null">
        is_es_check_pass = #{isEsCheckPass,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    WHERE id = #{id,jdbcType=INTEGER}
  </update>

</mapper>