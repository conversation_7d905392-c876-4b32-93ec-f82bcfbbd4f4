<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.tag.dao.tag.base.TagSchemaEnumDictDao">
  <resultMap id="BaseResultMap" type="com.ddmc.tag.model.base.TagSchemaEnumDict">
    <!--@mbg.generated 2020-04-26 14:28:27-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dict_type" jdbcType="TINYINT" property="dictType" />
    <result column="field_value" jdbcType="VARCHAR" property="fieldValue" />
    <result column="field_show_value" jdbcType="VARCHAR" property="fieldShowValue" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="is_valid" jdbcType="TINYINT" property="isValid" />
    <result column="last_editor" jdbcType="VARCHAR" property="lastEditor" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated 2020-04-26 14:28:27-->
    id, dict_type, field_value, field_show_value, note, is_valid, last_editor, create_time, 
    update_time
  </sql>
  <insert id="insert" parameterType="com.ddmc.tag.model.base.TagSchemaEnumDict">
    <!--@mbg.generated 2020-04-26 14:28:27-->
    INSERT INTO tag_schema_enum_dict
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      dict_type,
      <if test="fieldValue != null">
        field_value,
      </if>
      <if test="fieldShowValue != null">
        field_show_value,
      </if>
      <if test="note != null">
        note,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
      <if test="lastEditor != null">
        last_editor,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
      #{id,jdbcType=BIGINT},
      #{dictType,jdbcType=TINYINT},
      <if test="fieldValue != null">
        #{fieldValue,jdbcType=VARCHAR},
      </if>
      <if test="fieldShowValue != null">
        #{fieldShowValue,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        #{note,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=TINYINT},
      </if>
      <if test="lastEditor != null">
        #{lastEditor,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="insertList" parameterType="java.util.List">
    <!--@mbg.generated 2020-04-26 14:28:27-->
    INSERT INTO tag_schema_enum_dict (
      id, dict_type, field_value, field_show_value, note, is_valid, last_editor, create_time, 
      update_time
    ) VALUES 
    <foreach collection="list" index="index" item="item" separator=",">
      (
        #{item.id,jdbcType=BIGINT}, #{item.dictType,jdbcType=TINYINT}, #{item.fieldValue,jdbcType=VARCHAR},
        #{item.fieldShowValue,jdbcType=VARCHAR}, #{item.note,jdbcType=VARCHAR}, #{item.isValid,jdbcType=TINYINT}, 
        #{item.lastEditor,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>
  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated 2020-04-26 14:28:27-->
    SELECT <include refid="Base_Column_List"/> FROM tag_schema_enum_dict WHERE id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectOne" parameterType="com.ddmc.tag.model.base.TagSchemaEnumDict" resultMap="BaseResultMap">
    <!--@mbg.generated 2020-04-26 14:28:27-->
    SELECT <include refid="Base_Column_List"/> FROM tag_schema_enum_dict
    <where>
      <if test="dictType != null">
        AND dict_type = #{dictType,jdbcType=TINYINT}
      </if>
      <if test="fieldValue != null">
        AND field_value = #{fieldValue,jdbcType=VARCHAR}
      </if>
      <if test="fieldShowValue != null">
        AND field_show_value = #{fieldShowValue,jdbcType=VARCHAR}
      </if>
      <if test="note != null">
        AND note = #{note,jdbcType=VARCHAR}
      </if>
      <if test="isValid != null">
        AND is_valid = #{isValid,jdbcType=TINYINT}
      </if>
      <if test="lastEditor != null">
        AND last_editor = #{lastEditor,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        AND create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        AND update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      LIMIT 1
    </where>
  </select>
  <select id="selectList" parameterType="com.ddmc.tag.model.base.TagSchemaEnumDict" resultMap="BaseResultMap">
    <!--@mbg.generated 2020-04-26 14:28:27-->
    SELECT <include refid="Base_Column_List"/> FROM tag_schema_enum_dict
    <where>
      <if test="dictType != null">
        AND dict_type = #{dictType,jdbcType=TINYINT}
      </if>
      <if test="fieldValue != null">
        AND field_value = #{fieldValue,jdbcType=VARCHAR}
      </if>
      <if test="fieldShowValue != null">
        AND field_show_value = #{fieldShowValue,jdbcType=VARCHAR}
      </if>
      <if test="note != null">
        AND note = #{note,jdbcType=VARCHAR}
      </if>
      <if test="isValid != null">
        AND is_valid = #{isValid,jdbcType=TINYINT}
      </if>
      <if test="lastEditor != null">
        AND last_editor = #{lastEditor,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        AND create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        AND update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    <!--@mbg.generated 2020-04-26 14:28:27-->
    SELECT <include refid="Base_Column_List"/> FROM tag_schema_enum_dict
  </select>
  <select id="count" parameterType="com.ddmc.tag.model.base.TagSchemaEnumDict" resultType="java.lang.Long">
    <!--@mbg.generated 2020-04-26 14:28:27-->
    SELECT count(1) FROM tag_schema_enum_dict
    <where>
      <if test="id != null">
        AND id=#{id,jdbcType=VARCHAR}
      </if>
      <if test="dictType != null">
        AND dict_type=#{dictType,jdbcType=TINYINT}
      </if>
      <if test="fieldValue != null">
        AND field_value=#{fieldValue,jdbcType=VARCHAR}
      </if>
      <if test="fieldShowValue != null">
        AND field_show_value=#{fieldShowValue,jdbcType=VARCHAR}
      </if>
      <if test="note != null">
        AND note=#{note,jdbcType=VARCHAR}
      </if>
      <if test="isValid != null">
        AND is_valid=#{isValid,jdbcType=TINYINT}
      </if>
      <if test="lastEditor != null">
        AND last_editor=#{lastEditor,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        AND create_time=#{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        AND update_time=#{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </select>
  <delete id="deleteById" parameterType="java.lang.String">
    <!--@mbg.generated 2020-04-26 14:28:27-->
    DELETE FROM tag_schema_enum_dict WHERE id = #{id,jdbcType=VARCHAR}
  </delete>
  <update id="update" parameterType="com.ddmc.tag.model.base.TagSchemaEnumDict">
    <!--@mbg.generated 2020-04-26 14:28:27-->
    UPDATE tag_schema_enum_dict
    <set>
      <if test="dictType != null">
        dict_type = #{dictType,jdbcType=TINYINT},
      </if>
      <if test="fieldValue != null">
        field_value = #{fieldValue,jdbcType=VARCHAR},
      </if>
      <if test="fieldShowValue != null">
        field_show_value = #{fieldShowValue,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        note = #{note,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=TINYINT},
      </if>
      <if test="lastEditor != null">
        last_editor = #{lastEditor,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT}
  </update>
</mapper>