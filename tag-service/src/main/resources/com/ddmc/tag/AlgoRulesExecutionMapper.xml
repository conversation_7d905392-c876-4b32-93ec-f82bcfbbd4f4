<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.tag.dao.tag.rule.AlgoRulesExecutionMapper">

    <resultMap id="BaseResultMap" type="com.ddmc.tag.model.rule.AlgoRulesExecution">
        <result property="id" column="id"/>
        <result property="ruleId" column="rule_id"/>
        <result property="strategyId" column="strategy_id"/>
        <result property="lastExecId" column="last_exec_id"/>
        <result property="lastExecTime" column="last_exec_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        <trim suffixOverrides=",">
            `id`,
            `rule_id`,
            `strategy_id`,
            `last_exec_id`,
            `last_exec_time`,
            `create_time`,
            `update_time`,
        </trim>
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO `algo_rules_execution`
        (
        <trim suffixOverrides=",">
            <if test="ruleId != null">
                `rule_id`,
            </if>
            <if test="strategyId != null">
                `strategy_id`,
            </if>
            <if test="lastExecId != null">
                `last_exec_id`,
            </if>
            <if test="lastExecTime != null">
                `last_exec_time`,
            </if>
        </trim>
        )
        VALUES
        (
        <trim suffixOverrides=",">
            <if test="ruleId != null">
                #{ruleId},
            </if>
            <if test="strategyId != null">
                #{strategyId},
            </if>
            <if test="lastExecId != null">
                #{lastExecId},
            </if>
            <if test="lastExecTime != null">
                #{lastExecTime},
            </if>
        </trim>
        )
    </insert>


    <update id="update">
        UPDATE `algo_rules_execution`
        SET
        <trim suffixOverrides=",">
            <if test="ruleId != null">
                `rule_id` = #{ruleId},
            </if>
            <if test="strategyId != null and strategyId != ''">
                `strategy_id` = #{strategyId},
            </if>
            <if test="lastExecId != null and lastExecId != ''">
                `last_exec_id` = #{lastExecId},
            </if>
            <if test="lastExecTime != null">
                `last_exec_time` = #{lastExecTime},
            </if>
        </trim>
        WHERE id = #{id}
    </update>

    <select id="selectByRuleId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM algo_rules_execution
        <where>
            rule_id = #{ruleId,jdbcType=BIGINT}
        </where>
    </select>

    <select id="selectByStrategyId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM algo_rules_execution
        <where>
            strategy_id = #{strategyId,jdbcType=BIGINT}
        </where>
    </select>

    <select id="selectByRuleIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM algo_rules_execution
        <where>
            rule_id in
            <foreach collection="ruleIds" item="ruleId" open="(" close=")" separator=",">
                #{ruleId}
            </foreach>
        </where>
    </select>

</mapper>