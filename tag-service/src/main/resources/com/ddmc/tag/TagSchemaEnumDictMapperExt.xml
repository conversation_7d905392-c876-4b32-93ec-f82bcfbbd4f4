<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.tag.dao.tag.base.TagSchemaEnumDictDao">


    <select id="selectByDictTypes" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from
        tag_schema_enum_dict
        where
        dict_type in
        <foreach collection="dictTypes" item="dictType" open="(" separator="," close=")">
            #{dictType,jdbcType=TINYINT}
        </foreach>
        order by id ASC
    </select>
</mapper>