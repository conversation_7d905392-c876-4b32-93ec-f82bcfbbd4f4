<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.tag.dao.tag.stats.RuleDownloadsDao">
  <resultMap id="BaseResultMap" type="com.ddmc.tag.model.stats.RuleDownloads">
    <!--@mbg.generated 2020-08-06 20:07:35-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="result_id" jdbcType="BIGINT" property="resultId" />
    <result column="rule_id" jdbcType="BIGINT" property="ruleId" />
    <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="category_type" jdbcType="TINYINT" property="categoryType" />
    <result column="status" jdbcType="BIGINT" property="status" />
    <result column="download" jdbcType="VARCHAR" property="download" />
    <result column="stats_date" jdbcType="VARCHAR" property="statsDate" />
    <result column="stats_hour" jdbcType="VARCHAR" property="statsHour" />
    <result column="stats_time" jdbcType="TIMESTAMP" property="statsTime" />
    <result column="stats_count" jdbcType="INTEGER" property="statsCount" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="download_time" jdbcType="TIMESTAMP" property="downloadTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated 2020-08-06 20:07:35-->
    id, result_id, rule_id, rule_name, group_id, group_name, category_type, status, download, stats_date, stats_hour, stats_time, stats_count,
    creator, create_time, download_time
  </sql>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ddmc.tag.model.stats.RuleDownloads" useGeneratedKeys="true">
    <!--@mbg.generated 2020-08-06 20:07:35-->
    INSERT INTO stats_rule_downloads
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="resultId != null">
        result_id,
      </if>
      <if test="ruleId != null">
        rule_id,
      </if>
      <if test="ruleName != null">
        rule_name,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="groupName != null">
        group_name,
      </if>
      <if test="categoryType != null">
        category_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="download != null">
        download,
      </if>
      <if test="statsDate != null">
        stats_date,
      </if>
      <if test="statsHour != null">
        stats_hour,
      </if>
      <if test="statsTime != null">
        stats_time,
      </if>
      <if test="statsCount != null">
        stats_count,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="downloadTime != null">
        download_time,
      </if>
    </trim>
    VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="resultId != null">
        #{resultId,jdbcType=BIGINT},
      </if>
      <if test="ruleId != null">
        #{ruleId,jdbcType=BIGINT},
      </if>
      <if test="ruleName != null">
        #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="categoryType != null">
        #{categoryType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=BIGINT},
      </if>
      <if test="download != null">
        #{download,jdbcType=VARCHAR},
      </if>
      <if test="statsDate != null">
        #{statsDate,jdbcType=VARCHAR},
      </if>
      <if test="statsHour != null">
        #{statsHour,jdbcType=VARCHAR},
      </if>
      <if test="statsTime != null">
        #{statsTime,jdbcType=TIMESTAMP},
      </if>
      <if test="statsCount != null">
        #{statsCount,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="downloadTime != null">
        #{downloadTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="insertList" parameterType="java.util.List">
    <!--@mbg.generated 2020-08-06 20:07:35-->
    INSERT INTO stats_rule_downloads (
      result_id, rule_id, rule_name, group_id, group_name, category_type, status, download, stats_date, stats_hour, stats_time, stats_count, creator,
      create_time, download_time
    ) VALUES 
    <foreach collection="list" index="index" item="item" separator=",">
      (
        #{item.resultId,jdbcType=BIGINT}, #{item.ruleId,jdbcType=BIGINT}, #{item.ruleName,jdbcType=VARCHAR},
        #{item.groupId,jdbcType=BIGINT}, #{item.groupName,jdbcType=VARCHAR}, #{item.categoryType,jdbcType=TINYINT},
        #{item.status,jdbcType=BIGINT}, #{item.download,jdbcType=VARCHAR}, #{item.statsDate,jdbcType=VARCHAR},
        #{item.statsHour,jdbcType=VARCHAR}, #{item.statsTime,jdbcType=TIMESTAMP}, #{item.statsCount,jdbcType=INTEGER},
        #{item.creator,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.downloadTime,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>
  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated 2020-08-06 20:07:35-->
    SELECT <include refid="Base_Column_List"/> FROM stats_rule_downloads WHERE id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectOne" parameterType="com.ddmc.tag.model.stats.RuleDownloads" resultMap="BaseResultMap">
    <!--@mbg.generated 2020-08-06 20:07:35-->
    SELECT <include refid="Base_Column_List"/> FROM stats_rule_downloads
    <where>
      <if test="resultId != null">
        AND result_id = #{resultId,jdbcType=BIGINT}
      </if>
      <if test="ruleId != null">
        AND rule_id = #{ruleId,jdbcType=BIGINT}
      </if>
      <if test="ruleName != null">
        AND rule_name = #{ruleName,jdbcType=VARCHAR}
      </if>
      <if test="groupId != null">
        AND group_id = #{groupId,jdbcType=BIGINT}
      </if>
      <if test="groupName != null">
        AND group_name = #{groupName,jdbcType=VARCHAR}
      </if>
      <if test="categoryType != null">
        AND category_type = #{categoryType,jdbcType=TINYINT}
      </if>
      <if test="status != null">
        AND status = #{status,jdbcType=BIGINT}
      </if>
      <if test="download != null">
        AND download = #{download,jdbcType=VARCHAR}
      </if>
      <if test="statsDate != null">
        AND stats_date = #{statsDate,jdbcType=VARCHAR}
      </if>
      <if test="statsHour != null">
        AND stats_hour = #{statsHour,jdbcType=VARCHAR}
      </if>
      <if test="statsTime != null">
        AND stats_time = #{statsTime,jdbcType=TIMESTAMP}
      </if>
      <if test="statsCount != null">
        AND stats_count = #{statsCount,jdbcType=INTEGER}
      </if>
      <if test="creator != null">
        AND creator = #{creator,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        AND create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="downloadTime != null">
        AND download_time = #{downloadTime,jdbcType=TIMESTAMP}
      </if>
      LIMIT 1
    </where>
  </select>
  <select id="selectList" parameterType="com.ddmc.tag.model.stats.RuleDownloads" resultMap="BaseResultMap">
    <!--@mbg.generated 2020-08-06 20:07:35-->
    SELECT <include refid="Base_Column_List"/> FROM stats_rule_downloads
    <where>
      <if test="resultId != null">
        AND result_id = #{resultId,jdbcType=BIGINT}
      </if>
      <if test="ruleId != null">
        AND rule_id = #{ruleId,jdbcType=BIGINT}
      </if>
      <if test="ruleName != null">
        AND rule_name = #{ruleName,jdbcType=VARCHAR}
      </if>
      <if test="groupId != null">
        AND group_id = #{groupId,jdbcType=BIGINT}
      </if>
      <if test="groupName != null">
        AND group_name = #{groupName,jdbcType=VARCHAR}
      </if>
      <if test="categoryType != null">
        AND category_type = #{categoryType,jdbcType=TINYINT}
      </if>
      <if test="status != null">
        AND status = #{status,jdbcType=BIGINT}
      </if>
      <if test="download != null">
        AND download = #{download,jdbcType=VARCHAR}
      </if>
      <if test="statsDate != null">
        AND stats_date = #{statsDate,jdbcType=VARCHAR}
      </if>
      <if test="statsHour != null">
        AND stats_hour = #{statsHour,jdbcType=VARCHAR}
      </if>
      <if test="statsTime != null">
        AND stats_time = #{statsTime,jdbcType=TIMESTAMP}
      </if>
      <if test="statsCount != null">
        AND stats_count = #{statsCount,jdbcType=INTEGER}
      </if>
      <if test="creator != null">
        AND creator = #{creator,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        AND create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="downloadTime != null">
        AND download_time = #{downloadTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    <!--@mbg.generated 2020-08-06 20:07:35-->
    SELECT <include refid="Base_Column_List"/> FROM stats_rule_downloads
  </select>
  <select id="count" parameterType="com.ddmc.tag.model.stats.RuleDownloads" resultType="java.lang.Long">
    <!--@mbg.generated 2020-08-06 20:07:35-->
    SELECT count(1) FROM stats_rule_downloads
    <where>
      <if test="id != null">
        AND id=#{id,jdbcType=BIGINT}
      </if>
      <if test="resultId != null">
        AND result_id=#{resultId,jdbcType=BIGINT}
      </if>
      <if test="ruleId != null">
        AND rule_id=#{ruleId,jdbcType=BIGINT}
      </if>
      <if test="ruleName != null">
        AND rule_name = #{ruleName,jdbcType=VARCHAR}
      </if>
      <if test="groupId != null">
        AND group_id=#{groupId,jdbcType=BIGINT}
      </if>
      <if test="groupName != null">
        AND group_name = #{groupName,jdbcType=VARCHAR}
      </if>
      <if test="categoryType != null">
        AND category_type=#{categoryType,jdbcType=TINYINT}
      </if>
      <if test="status != null">
        AND status=#{status,jdbcType=BIGINT}
      </if>
      <if test="download != null">
        AND download=#{download,jdbcType=VARCHAR}
      </if>
      <if test="statsDate != null">
        AND stats_date=#{statsDate,jdbcType=VARCHAR}
      </if>
      <if test="statsHour != null">
        AND stats_hour=#{statsHour,jdbcType=VARCHAR}
      </if>
      <if test="statsTime != null">
        AND stats_time = #{statsTime,jdbcType=TIMESTAMP}
      </if>
      <if test="statsCount != null">
        AND stats_count=#{statsCount,jdbcType=INTEGER}
      </if>
      <if test="creator != null">
        AND creator=#{creator,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        AND create_time=#{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="downloadTime != null">
        AND download_time=#{downloadTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </select>
  <delete id="deleteById" parameterType="java.lang.Long">
    <!--@mbg.generated 2020-08-06 20:07:35-->
    DELETE FROM stats_rule_downloads WHERE id = #{id,jdbcType=BIGINT}
  </delete>
  <update id="update" parameterType="com.ddmc.tag.model.stats.RuleDownloads">
    <!--@mbg.generated 2020-08-06 20:07:35-->
    UPDATE stats_rule_downloads
    <set>
      <if test="resultId != null">
        result_id = #{resultId,jdbcType=BIGINT},
      </if>
      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=BIGINT},
      </if>
      <if test="ruleName != null">
        rule_name = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="categoryType != null">
        category_type = #{categoryType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=BIGINT},
      </if>
      <if test="download != null">
        download = #{download,jdbcType=VARCHAR},
      </if>
      <if test="statsDate != null">
        stats_date = #{statsDate,jdbcType=VARCHAR},
      </if>
      <if test="statsHour != null">
        stats_hour = #{statsHour,jdbcType=VARCHAR},
      </if>
      <if test="statsTime != null">
        stats_time = #{statsTime,jdbcType=TIMESTAMP},
      </if>
      <if test="statsCount != null">
        stats_count = #{statsCount,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="downloadTime != null">
        download_time = #{downloadTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT}
  </update>
</mapper>