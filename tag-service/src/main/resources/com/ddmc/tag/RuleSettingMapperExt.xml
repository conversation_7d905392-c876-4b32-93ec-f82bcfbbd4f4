<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.tag.dao.tag.rule.RuleSettingDao">

    <sql id="Page_Column">
        id, group_id, name, update_type, note, download, status, data_status, is_valid, creator_name, creator_email,
        create_time, update_time, category_type, rule_status, select_type, scene, match_read, launch_tag, is_union,
        special_scenes,sync_dw,sync_kepler,last_edit_time,is_wecom,tenant_id,is_algo
    </sql>

    <select id="countRuleSetting" resultType="java.lang.Long">
        SELECT count(1) FROM rule_setting
        <where>
            is_valid = 1


            <if test="ruleIds != null and ruleIds.size() > 0">
                AND id IN
                <foreach collection="ruleIds" item="ruleId" open="(" close=")" separator=",">
                    #{ruleId,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="updateType != null">
                AND update_type = #{updateType,jdbcType=TINYINT}
            </if>


            <if test="groupId != null">
                AND group_id = #{groupId,jdbcType=BIGINT}
            </if>
            <if test="creatorName != null">
                AND creator_name LIKE CONCAT('%', #{creatorName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="name != null">
                AND name LIKE CONCAT('%', #{name,jdbcType=VARCHAR}, '%')
            </if>
            <if test="createTimeStart != null">
                AND create_time &gt;= #{createTimeStart,jdbcType=TIMESTAMP}
            </if>
            <if test="createTimeEnd != null">
                AND create_time &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
            </if>
            <if test="status != null and status.size() > 0">
                AND rule_status in
                <foreach collection="status" item="s" open="(" close=")" separator=",">
                    #{s,jdbcType=TINYINT}
                </foreach>
            </if>
        </where>
    </select>

    <select id="countRuleSettingV2" resultType="java.lang.Long">
        SELECT count(1) FROM rule_setting
        <where>
            is_valid = 1
            <if test="updateType != null">
                AND update_type = #{updateType,jdbcType=TINYINT}
            </if>
            <if test="selectType != null">
                AND select_type = #{selectType,jdbcType=TINYINT}
            </if>

            <if test="groupId != null">
                AND group_id = #{groupId,jdbcType=BIGINT}
            </if>
            <if test="creatorName != null">
                AND creator_email=  #{creatorName,jdbcType=VARCHAR}
            </if>

            <if test="ruleIdOrName != null">
                AND (id = #{ruleIdOrName} or name LIKE CONCAT('%', #{ruleIdOrName,jdbcType=VARCHAR}, '%'))
            </if>

            <if test="createTimeStart != null">
                AND create_time &gt;= #{createTimeStart,jdbcType=TIMESTAMP}
            </if>
            <if test="createTimeEnd != null">
                AND create_time &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
            </if>
            <if test="status != null">
                AND rule_status = #{status,jdbcType=TINYINT}
            </if>
            <if test="categoryType != null">
                AND category_type = #{categoryType,jdbcType=TINYINT}
            </if>
            <if test="effectType != null">
                AND effect_type = #{effectType,jdbcType=TINYINT}
            </if>
        </where>
    </select>


    <select id="queryRuleSetting" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM rule_setting
        <where>
            is_valid = 1

            <if test="ruleIds != null and ruleIds.size() > 0">
                AND id IN
                <foreach collection="ruleIds" item="ruleId" open="(" close=")" separator=",">
                    #{ruleId,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="updateType != null">
                AND update_type = #{updateType,jdbcType=TINYINT}
            </if>

        <if test="groupId != null">
            AND group_id = #{groupId,jdbcType=BIGINT}
        </if>
        <if test="creatorName != null">
            AND creator_name LIKE CONCAT('%', #{creatorName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="name != null">
            AND name LIKE CONCAT('%', #{name,jdbcType=VARCHAR}, '%')
        </if>
        <if test="createTimeStart != null">
            AND create_time &gt;= #{createTimeStart,jdbcType=TIMESTAMP}
        </if>
        <if test="createTimeEnd != null">
            AND create_time &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
        </if>
        <if test="status != null and status.size() > 0">
            AND rule_status in
            <foreach collection="status" item="s" open="(" close=")" separator=",">
                #{s,jdbcType=TINYINT}
            </foreach>

        </if>
        </where>
        ORDER BY create_time DESC LIMIT #{start}, #{pageSize}
    </select>

    <select id="queryRuleSettingV2" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM rule_setting
        <where>
            is_valid = 1

            <if test="updateType != null">
                AND update_type = #{updateType,jdbcType=TINYINT}
            </if>
            <if test="selectType != null">
                AND select_type = #{selectType,jdbcType=TINYINT}
            </if>
            <if test="groupId != null">
                AND group_id = #{groupId,jdbcType=BIGINT}
            </if>
            <if test="creatorName != null">
                AND creator_email=#{creatorName,jdbcType=VARCHAR}
            </if>
            <if test="ruleIdOrName != null">
                AND (id = #{ruleIdOrName} or name LIKE CONCAT('%', #{ruleIdOrName,jdbcType=VARCHAR}, '%'))
            </if>
            <if test="createTimeStart != null">
                AND create_time &gt;= #{createTimeStart,jdbcType=TIMESTAMP}
            </if>
            <if test="createTimeEnd != null">
                AND create_time &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
            </if>
            <if test="status != null">
                AND rule_status = #{status,jdbcType=TINYINT}
            </if>
            <if test="categoryType != null">
                AND category_type = #{categoryType,jdbcType=TINYINT}
            </if>
            <if test="effectType != null">
                AND effect_type = #{effectType,jdbcType=TINYINT}
            </if>
        </where>
        ORDER BY create_time DESC LIMIT #{start}, #{pageSize}
    </select>
    <!--
    select A.*, if(isnull(B.is_top),0,1) is_top from ddmc_tag.rule_setting A left join (
    select 1 as is_top, id b_rule_id from ddmc_tag.rule_setting where id in (125096, 125054)
    ) B
    on A.id = B.b_rule_id
    where group_id = 1
    order by is_top desc, id desc limit 10
    -->

    <select id="queryRuleSetting4Top" resultMap="BaseResultMap">

        SELECT
        <include refid="Base_Column_List"/> , case ifnull(B.b_rule_id,0) when 0 then 0 else 1 end as is_top
        FROM rule_setting A LEFT JOIN (
        SELECT id b_rule_id FROM rule_setting WHERE
            id IN
            <foreach collection="topRuleIds" item="topRuleId" open="(" close=")" separator=",">
                #{topRuleId,jdbcType=BIGINT}
            </foreach>
        ) B ON A.id = B.b_rule_id
        <where>
            is_valid = 1

            <if test="ruleIds != null and ruleIds.size() > 0">
                AND id IN
                <foreach collection="ruleIds" item="ruleId" open="(" close=")" separator=",">
                    #{ruleId,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="updateType != null">
                AND update_type = #{updateType,jdbcType=TINYINT}
            </if>


            <if test="groupId != null">
                AND group_id = #{groupId,jdbcType=BIGINT}
            </if>
            <if test="creatorName != null">
                AND creator_name LIKE CONCAT('%', #{creatorName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="name != null">
                AND name LIKE CONCAT('%', #{name,jdbcType=VARCHAR}, '%')
            </if>
            <if test="createTimeStart != null">
                AND create_time &gt;= #{createTimeStart,jdbcType=TIMESTAMP}
            </if>
            <if test="createTimeEnd != null">
                AND create_time &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
            </if>
            <if test="status != null and status.size() > 0">
                AND rule_status in
                <foreach collection="status" item="s" open="(" close=")" separator=",">
                    #{s,jdbcType=TINYINT}
                </foreach>

            </if>
        </where>
        ORDER BY is_top DESC, create_time DESC LIMIT #{start}, #{pageSize}
    </select>



    <select id="queryRuleSettingByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM rule_setting
        <where>
            <foreach collection="ids" item="id" index="index" separator="OR">
                (id = #{id})
            </foreach>
        </where>
    </select>

    <select id="queryRuleSettingByIdsAndCategory" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM rule_setting
        <where>
            category_type = #{categoryType,jdbcType=TINYINT}
            and
            <foreach collection="ids" item="id" index="index" separator="OR">
                (id = #{id})
            </foreach>
        </where>
    </select>

    <select id="queryRuleIdsByIdsAndCategoryAndReadMatch" resultType="java.lang.Long">
        SELECT
        id
        FROM rule_setting
        <where>
            category_type = #{categoryType,jdbcType=TINYINT}
            and
            match_read = 1
            and
            <foreach collection="ids" item="id" index="index" separator="OR">
                (id = #{id})
            </foreach>
        </where>
    </select>

    <select id="queryReadMatchById" resultType="java.lang.Integer">
        SELECT
        match_read
        FROM rule_setting
        <where>
            id = #{id,jdbcType=BIGINT}
        </where>
    </select>

    <select id="queryReadMatchByIds" resultMap="BaseResultMap">
        SELECT
        id,
        match_read
        FROM rule_setting
        <where>
            is_valid = true
            and
            rule_status = 1
            <if test="ids != null">
                AND
                <foreach collection="ids" item="id" index="index" separator="OR">
                    (id = #{id})
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryRuleInfoByIds" resultMap="BaseResultMap">
        SELECT
        id,
        is_include_near_real_time_tag,
        match_read,
        launch_tag,
        is_union
        FROM rule_setting
        <where>
            is_valid = true
            and
            rule_status = 1
            <if test="ids != null">
                AND
                <foreach collection="ids" item="id" index="index" separator="OR">
                    (id = #{id})
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryRuleSettingByIdsAndUpdateType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM rule_setting
        <where>
            update_type = #{updateType,jdbcType=TINYINT}
            and
            <foreach collection="ids" item="id" index="index" separator="OR">
                (id = #{id})
            </foreach>
        </where>
    </select>

    <update id="updateStatusByIds">
        UPDATE rule_setting
        SET status = #{status,jdbcType=TINYINT},
        <if test="lastEditorEmail != null">
            last_editor_email = #{lastEditorEmail,jdbcType=VARCHAR},
        </if>
        update_time = NOW()
        WHERE

        <foreach collection="ids" item="id" index="index" separator="OR">
            (id = #{id})
        </foreach>

    </update>

    <update id="updateRuleStatusByIds">
        UPDATE rule_setting
        SET rule_status = #{ruleStatus,jdbcType=TINYINT},
        <if test="lastEditorEmail != null">
            last_editor_email = #{lastEditorEmail,jdbcType=VARCHAR},
        </if>
        update_time = NOW()
        WHERE

        <foreach collection="ids" item="id" index="index" separator="OR">
            (id = #{id})
        </foreach>

    </update>

    <update id="updateRuleStatusAndLastEditTimeByIds">
        UPDATE rule_setting
        SET rule_status = #{ruleStatus,jdbcType=TINYINT},
        <if test="lastEditorEmail != null">
            last_editor_email = #{lastEditorEmail,jdbcType=VARCHAR},
        </if>
        update_time = NOW(),
        last_edit_time = NOW()
        WHERE
        <foreach collection="ids" item="id" index="index" separator="OR">
            (id = #{id})
        </foreach>
    </update>

    <update id="updateDataStatusByIds">
        UPDATE rule_setting
        SET data_status = #{dataStatus,jdbcType=TINYINT},
        <if test="lastEditorEmail != null">
            last_editor_email = #{lastEditorEmail,jdbcType=VARCHAR},
        </if>
        update_time = NOW()
        WHERE

        <foreach collection="ids" item="id" index="index" separator="OR">
            (id = #{id})
        </foreach>

    </update>

    <update id="dataOverByIds">
        UPDATE rule_setting
        SET data_status = 2,
            download = #{download,jdbcType=VARCHAR},
            update_time = NOW()
        WHERE

        <foreach collection="ids" item="id" index="index" separator="OR">
            (id = #{id})
        </foreach>

    </update>

    <update id="inValidByIds">
        UPDATE rule_setting
        SET is_valid = false,
        <if test="lastEditorEmail != null">
            last_editor_email = #{lastEditorEmail,jdbcType=VARCHAR},
        </if>
        update_time = NOW()
        WHERE

        <foreach collection="ids" item="id" index="index" separator="OR">
            (id = #{id})
        </foreach>

    </update>

    <select id="countByGroupId" resultType="java.lang.Integer">
        select count(1)
        from  rule_setting
        where is_valid = true
        and group_id = #{groupId,jdbcType=BIGINT}
    </select>

    <select id="findUpdateDailyIds" resultType="java.lang.Long" >
        select id
        from rule_setting
        where is_valid = true
        and update_type = 0
        and rule_status = 1
        and is_include_real_time_tag = 0
        and select_type &lt;&gt; 3
        and category_type = #{categoryType,jdbcType=TINYINT}
    </select>

    <select id="findAllUpdateDailyIds" resultType="java.lang.Long" >
        select id
        from rule_setting
        where is_valid = true
        and category_type = #{categoryType,jdbcType=TINYINT}
        and update_type = 0
        and select_type &lt;&gt; 3
        and rule_status = 1
    </select>

    <update id="updateStatusByRuleType">
        update rule_setting
        set data_status = #{dataStatus,jdbcType=TINYINT}
        where update_type = #{updateType,jdbcType=TINYINT}
        and rule_status in
        <foreach collection="statusSet" item="status" open="(" close=")" separator=",">
            #{status,jdbcType=TINYINT}
        </foreach>
        and category_type = #{categoryType,jdbcType=TINYINT}
        and is_valid = true
        and is_include_real_time_tag = 0
        and select_type &lt;&gt; 3
    </update>

    <update id="updateStatusByRuleTypeForAll">
        update rule_setting
        set data_status = #{dataStatus,jdbcType=TINYINT}
        where update_type = #{updateType,jdbcType=TINYINT}
        and rule_status in
        <foreach collection="statusSet" item="status" open="(" close=")" separator=",">
            #{status,jdbcType=TINYINT}
        </foreach>
        and category_type = #{categoryType,jdbcType=TINYINT}
        and is_valid = true
        and select_type &lt;&gt; 3
    </update>

    <update id="clearGroupId">
        update rule_setting
        set group_id = 0
        where group_id = #{groupId}
        and category_type = #{categoryType,jdbcType=TINYINT}
    </update>

    <select id="countRule4AdvPop" resultType="java.lang.Long">
        SELECT count(1) FROM rule_setting
        <where>
            is_valid = 1
            <if test="categoryType != null">
                AND category_type = #{categoryType,jdbcType=TINYINT}
            </if>
            <if test="groupId != null and groupId > 0">
                AND group_id = #{groupId,jdbcType=BIGINT}
            </if>
            <if test="name != null">
                AND
                (
                    id = #{name,jdbcType=VARCHAR}
                    OR
                    name LIKE CONCAT('%', #{name,jdbcType=VARCHAR}, '%')
                    OR
                    creator_name LIKE CONCAT('%', #{name,jdbcType=VARCHAR}, '%')
                )
            </if>
            <if test="creatorEmail != null">
                AND creator_email = #{creatorEmail,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="queryRule4AdvPop" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM rule_setting
        <where>
            is_valid = 1
            <if test="groupId != null and groupId > 0">
                AND group_id = #{groupId,jdbcType=BIGINT}
            </if>
            <if test="name != null">
                AND
                (
                id = #{name,jdbcType=VARCHAR}
                OR
                name LIKE CONCAT('%', #{name,jdbcType=VARCHAR}, '%')
                OR
                creator_name LIKE CONCAT('%', #{name,jdbcType=VARCHAR}, '%')
                )
            </if>
            <if test="creatorEmail != null">
                AND creator_email = #{creatorEmail,jdbcType=VARCHAR}
            </if>
            <if test="categoryType != null">
                AND category_type = #{categoryType,jdbcType=TINYINT}
            </if>
        </where>
        ORDER BY create_time DESC LIMIT #{start}, #{pageSize}
    </select>

    <select id="queryDeleteDataByTime" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from rule_setting
        where is_valid = false
        and update_time > #{minUpdateTime}
        category_type = #{categoryType,jdbcType=TINYINT}
    </select>

    <select id="findByCategoryId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from rule_setting
        where is_valid = true
        and category_type = #{categoryType,jdbcType=TINYINT}
    </select>

    <select id="findRealtimeAndStationRules" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from rule_setting
        where is_valid = true
        and category_type = 1
        and rule_status = 1
        and ( is_include_real_time_tag = 1 or is_include_station_tag = 1 )
    </select>

    <select id="findFieldsIncludedRules" resultType="java.lang.Long">
        SELECT id
        FROM rule_setting
        WHERE category_type = 1
        AND is_valid = true
        AND rule_status = 1
        AND update_type = 0
        AND select_type &lt;&gt; 3
        AND id IN
        (
            SELECT distinct rule_id
            FROM rule_setting_detail
            WHERE
            <foreach collection="fields" item="field" index="index" separator="OR">
                (field_name = #{field})
            </foreach>
            AND is_valid = 1
        )
    </select>

    <select id="selectGroupIdsByIds" resultMap="BaseResultMap">
        SELECT
        id, group_id, scene
        FROM rule_setting
        <where>
            <foreach collection="ids" item="id" index="index" separator="OR">
                (id = #{id})
            </foreach>
        </where>
    </select>


    <select id="selectByRuleStatusAndEffectType" resultMap="BaseResultMap" >
        select <include refid="Base_Column_List"/>
        from rule_setting
        where is_valid = true
          and rule_status = #{ruleStatus,jdbcType=TINYINT}
          and effect_type = #{effectType,jdbcType=TINYINT}
    </select>

    <select id="selectByRuleStatus" resultMap="BaseResultMap" >
        select <include refid="Base_Column_List"/>
        from rule_setting
        where is_valid = true
        and rule_status = #{ruleStatus,jdbcType=TINYINT}
    </select>


    <select id="selectByRuleNameAndCategoryType" resultMap="BaseResultMap" >
        select <include refid="Base_Column_List"/>
        from rule_setting
        where is_valid = true
        and name = #{name,jdbcType=VARCHAR}
        and category_type = #{categoryType,jdbcType=TINYINT}
    </select>

    <update id="updateList">
        <foreach collection="ruleSettingList" item="item" index="index" open="" close="" separator=";">
            update rule_setting
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.groupId != null">
                    group_id = #{item.groupId,jdbcType=BIGINT},
                </if>
                <if test="item.name != null">
                    name = #{item.name,jdbcType=VARCHAR},
                </if>
                <if test="item.updateType != null">
                    update_type = #{item.updateType,jdbcType=TINYINT},
                </if>
                <if test="item.isIncludeRealTimeTag != null">
                    is_include_real_time_tag = #{item.isIncludeRealTimeTag,jdbcType=TINYINT},
                </if>
                <if test="item.isIncludeNearRealTimeTag != null">
                    is_include_near_real_time_tag = #{item.isIncludeNearRealTimeTag,jdbcType=TINYINT},
                </if>
                <if test="item.isIncludeStationTag != null">
                    is_include_station_tag = #{item.isIncludeStationTag,jdbcType=TINYINT},
                </if>
                <if test="item.note != null">
                    note = #{item.note,jdbcType=VARCHAR},
                </if>
                <if test="item.download != null">
                    download = #{item.download,jdbcType=VARCHAR},
                </if>
                <if test="item.status != null">
                    status = #{item.status,jdbcType=TINYINT},
                </if>
                <if test="item.dataStatus != null">
                    data_status = #{item.dataStatus,jdbcType=TINYINT},
                </if>
                <if test="item.isValid != null">
                    is_valid = #{item.isValid,jdbcType=TINYINT},
                </if>
                <if test="item.creatorEmail != null">
                    creator_email = #{item.creatorEmail,jdbcType=VARCHAR},
                </if>
                <if test="item.creatorName != null">
                    creator_name = #{item.creatorName,jdbcType=VARCHAR},
                </if>
                <if test="item.lastEditorEmail != null">
                    last_editor_email = #{item.lastEditorEmail,jdbcType=VARCHAR},
                </if>
                <if test="item.ownerEmail != null">
                    owner_email = #{item.ownerEmail,jdbcType=VARCHAR},
                </if>
                <if test="item.ownerName != null">
                    owner_name = #{item.ownerName,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.categoryType != null">
                    category_type = #{item.categoryType,jdbcType=TINYINT},
                </if>
                <if test="item.effectType != null">
                    effect_type = #{item.effectType,jdbcType=TINYINT},
                </if>
                <if test="item.effectStartDate != null">
                    effect_start_date = #{item.effectStartDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.effectEndDate != null">
                    effect_end_date = #{item.effectEndDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.selectType != null">
                    select_type = #{item.selectType,jdbcType=TINYINT},
                </if>
                <if test="item.ruleStatus != null">
                    rule_status = #{item.ruleStatus,jdbcType=TINYINT},
                </if>
                <if test="item.scene != null">
                    scene = #{item.scene,jdbcType=TINYINT},
                </if>
                <if test="item.matchRead != null">
                    match_read = #{item.matchRead,jdbcType=TINYINT},
                </if>
                <if test="item.source != null">
                    source = #{item.source,jdbcType=TINYINT},
                </if>
                <if test="item.launchTag != null">
                    launch_tag = #{item.launchTag,jdbcType=TINYINT},
                </if>
                <if test="item.isUnion != null">
                    is_union = #{item.isUnion,jdbcType=TINYINT},
                </if>
                <if test="item.specialScenes != null">
                    special_scenes = #{item.specialScenes,jdbcType=VARCHAR},
                </if>
                <if test="item.syncDw != null">
                    sync_dw = #{item.syncDw,jdbcType=TINYINT},
                </if>
                <if test="item.syncKepler != null">
                    sync_kepler = #{item.syncKepler,jdbcType=TINYINT},
                </if>
                <if test="item.lastEditTime != null">
                    last_edit_time = #{item.lastEditTime,jdbcType=TINYINT},
                </if>
            </trim>
            where id= #{item.id}
        </foreach>
    </update>


    <select id="queryPageByRuleId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rule_setting
        where id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by create_time desc
        limit #{start},#{end}
    </select>

    <select id="queryByGroupIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM rule_setting
        <where>
            is_valid = true
            and
            rule_status = 1
            <if test="groupIdLit != null">
                AND
                group_id IN
                <foreach collection="groupIdLit" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryByRuleStatusAndCreateTime" resultMap="BaseResultMap" >
        select <include refid="Base_Column_List"/>
        from rule_setting
        where is_valid = true
        and rule_status = #{ruleStatus,jdbcType=TINYINT}
        and create_time &lt; date_sub(now(),interval #{days} day)
    </select>

    <update id="updateStatusByWithoutStationRuleType">
        update rule_setting
        set data_status = #{dataStatus,jdbcType=TINYINT}
        where update_type = #{updateType,jdbcType=TINYINT}
        and rule_status in
        <foreach collection="statusSet" item="status" open="(" close=")" separator=",">
            #{status,jdbcType=TINYINT}
        </foreach>
        and category_type = #{categoryType,jdbcType=TINYINT}
        and is_valid = true
        and is_include_real_time_tag = 0
        and scene = 0
    </update>

    <select id="findUpdateDailyWithoutStationIds" resultType="java.lang.Long" >
        select id
        from rule_setting
        where is_valid = true
        and category_type = #{categoryType,jdbcType=TINYINT}
        and update_type = 0
        and rule_status = 1
        and scene = 0
        and is_include_real_time_tag = 0
    </select>

    <select id="queryUsingTagTopNInValidRules" resultType="java.lang.String" >
        SELECT
            tmp.field_name
        FROM
            (SELECT
                rsd.field_name,
                COUNT(rsd.id) as total
            FROM
                rule_setting rs
            LEFT JOIN
                rule_setting_detail rsd
            ON
                rs.id = rsd.rule_id
            WHERE
                rs.rule_status = 1
            AND
                rs.is_valid = 1
        <if test="categoryType != null">
            AND
                rs.category_type = #{categoryType,jdbcType=TINYINT}
        </if>
        <if test="scene != null">
            AND
                rs.scene = #{scene,jdbcType=TINYINT}
        </if>
            AND
                rsd.is_valid = 1
            AND
                rsd.field_name != ''
            GROUP BY
                rsd.field_name
            ORDER BY
                total
            DESC) tmp
        <if test="limit != null">
            LIMIT #{limit,jdbcType=BIGINT}
        </if>
    </select>

    <update id="updateRuleSettingUnionStatus">
        update rule_setting
        set is_union = #{isUnion,jdbcType=TINYINT}
        where id IN
        <foreach collection="ruleIds" item="ruleId" open="(" close=")" separator=",">
            #{ruleId,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="queryAllValidAlgoGenerateRules" resultMap="BaseResultMap" >
        select <include refid="Base_Column_List"/>
        from rule_setting
        WHERE is_valid = true
        AND rule_status = 1
        AND select_type = 3
        AND update_type = 0
    </select>

    <select id="findAllDailyExecutingRulesCount" resultType="java.lang.Long" >
        select
        count(1)
        from rule_setting
        where is_valid = true
        and category_type = #{categoryType,jdbcType=TINYINT}
        and update_type = 0
        and select_type &lt;&gt; 3
        and rule_status = 1
        and data_status = 1
    </select>

</mapper>