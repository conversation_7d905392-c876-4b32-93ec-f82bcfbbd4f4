<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.tag.dao.tag.stats.StatsRuleOfflineConfirmDao">
    <resultMap id="BaseResultMap" type="com.ddmc.tag.model.stats.StatsRuleOfflineConfirm">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="rule_id" jdbcType="BIGINT" property="ruleId" />
        <result column="confirm_info" jdbcType="TINYINT" property="confirmInfo" />
        <result column="confirmed_by" jdbcType="VARCHAR" property="confirmedBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, rule_id, confirm_info, confirmed_by, create_time, update_time
    </sql>
    <insert id="insert" parameterType="com.ddmc.tag.model.stats.StatsRuleOfflineConfirm" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO stats_rule_offline_confirm
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">
                rule_id,
            </if>
            <if test="confirmInfo != null">
                confirm_info,
            </if>
            <if test="confirmedBy != null">
                confirmed_by,
            </if>
            <if test="reqHeader != null">
                req_header,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">
                #{ruleId,jdbcType=BIGINT},
            </if>
            <if test="confirmInfo != null">
                #{confirmInfo,jdbcType=TINYINT},
            </if>
            <if test="confirmedBy != null">
                #{confirmedBy,jdbcType=VARCHAR},
            </if>
            <if test="reqHeader != null">
                #{reqHeader,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </trim>
    </insert>

    <insert id="insertList" parameterType="java.util.List">
        INSERT INTO stats_rule_offline_confirm (
        rule_id,
        confirm_info
        confirmed_by,
        create_time,
        update_time
        ) VALUES
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.ruleId,jdbcType=BIGINT},
            #{item.confirmInfo,jdbcType=TINYINT},
            #{item.confirmedBy,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM stats_rule_offline_confirm WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectList" parameterType="com.ddmc.tag.model.stats.StatsRuleOfflineConfirm" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM stats_rule_offline_confirm
        <where>
            <if test="ruleId != null">
                AND rule_id = #{ruleId,jdbcType=BIGINT}
            </if>
            <if test="confirmInfo != null">
                AND confirm_info = #{confirmInfo,jdbcType=TINYINT}
            </if>
            <if test="confirmedBy != null">
                AND confirmed_by = #{confirmedBy,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                AND create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                AND update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM stats_rule_offline_confirm where 1 = 1
    </select>

    <update id="update" parameterType="com.ddmc.tag.model.stats.StatsRuleOfflineConfirm">
        UPDATE stats_rule_offline_confirm
        <set>
            <if test="ruleId != null">
                rule_id = #{ruleId,jdbcType=BIGINT},
            </if>
            <if test="confirmInfo != null">
                confirm_info = #{confirmInfo,jdbcType=TINYINT},
            </if>
            <if test="confirmedBy != null">
                confirmed_by = #{confirmedBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <insert id="saveOrUpdateBatch" parameterType="com.ddmc.tag.model.stats.StatsRuleOfflineConfirm">
        INSERT INTO stats_rule_offline_confirm (rule_id, confirm_info, confirmed_by, create_time, update_time)
        VALUES
        <foreach collection="stats" item="item" separator=",">
            (#{item.ruleId}, #{item.confirmInfo}, #{item.confirmedBy}, #{item.createTime}, #{item.updateTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        update_time = NOW()
    </insert>

    <select id="selectOfflineConfirmRulesYesByRuleIdsAndCreateTime" resultType="java.lang.Long">
        SELECT rule_id
        FROM stats_rule_offline_confirm
        WHERE create_time &gt;= date_sub(now(),interval #{days} day)
        AND confirm_info = 1
        AND rule_id IN
        <foreach collection="ruleIds" item="ruleId" open="(" close=")" separator=",">
            #{ruleId,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectOfflineConfirmCountByRuleIdAndCreateTime" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM stats_rule_offline_confirm
        WHERE create_time &gt;= date_sub(now(),interval #{days} day)
        AND rule_id = #{ruleId,jdbcType=BIGINT}
    </select>

</mapper>