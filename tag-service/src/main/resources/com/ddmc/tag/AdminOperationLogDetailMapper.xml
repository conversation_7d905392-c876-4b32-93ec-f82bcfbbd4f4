<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.tag.dao.tag.log.AdminOperationLogDetailDao">
  <resultMap id="BaseResultMap" type="com.ddmc.tag.model.log.AdminOperationLogDetail">

    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="operation_log_id" jdbcType="BIGINT" property="operationLogId" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="field_name_comment" jdbcType="VARCHAR" property="fieldNameComment" />
    <result column="old_value" jdbcType="VARCHAR" property="oldValue" />
    <result column="new_value" jdbcType="VARCHAR" property="newValue" />
  </resultMap>

  <sql id="Base_Column_List">
    id, operation_log_id, field_name, field_name_comment, old_value, new_value
  </sql>

  <insert id="insert" parameterType="com.ddmc.tag.model.log.AdminOperationLogDetail">
    INSERT INTO admin_operation_log_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="operationLogId != null">
        operation_log_id,
      </if>
      <if test="fieldName != null">
        field_name,
      </if>
      <if test="fieldNameComment != null">
        field_name_comment,
      </if>
      <if test="oldValue != null">
        old_value,
      </if>
      <if test="newValue != null">
        new_value,
      </if>
    </trim>
    VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="operationLogId != null">
        #{operationLogId,jdbcType=BIGINT},
      </if>
      <if test="fieldName != null">
        #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="fieldNameComment != null">
        #{fieldNameComment,jdbcType=VARCHAR},
      </if>
      <if test="oldValue != null">
        #{oldValue,jdbcType=VARCHAR},
      </if>
      <if test="newValue != null">
        #{newValue,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <insert id="insertList" parameterType="java.util.List">
    <!--@mbg.generated 2020-04-26 14:36:30-->
    INSERT INTO admin_operation_log_detail (
    operation_log_id, field_name, field_name_comment, old_value, new_value
    ) VALUES 
    <foreach collection="list" index="index" item="item" separator=",">
      (
        #{item.operationLogId,jdbcType=BIGINT},
        #{item.fieldName,jdbcType=VARCHAR},#{item.fieldNameComment,jdbcType=VARCHAR},
        #{item.oldValue,jdbcType=VARCHAR}, #{item.newValue,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM admin_operation_log_detail WHERE id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectOne" parameterType="com.ddmc.tag.model.log.AdminOperationLogDetail" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM admin_operation_log_detail
    <where>
      <if test="operationLogId != null">
        AND operation_log_id = #{operationLogId,jdbcType=BIGINT}
      </if>
      LIMIT 1
    </where>
  </select>
  <select id="selectList" parameterType="com.ddmc.tag.model.log.AdminOperationLogDetail" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM admin_operation_log_detail
    <where>
      <if test="operationLogId != null">
        AND operation_log_id = #{operationLogId,jdbcType=BIGINT}
      </if>
    </where>
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM admin_operation_log_detail
  </select>
  <select id="count" parameterType="com.ddmc.tag.model.log.AdminOperationLogDetail" resultType="java.lang.Long">
    SELECT count(1) FROM admin_operation_log_detail
    <where>
      <if test="id != null">
        AND id=#{id,jdbcType=BIGINT}
      </if>
      <if test="operationLogId != null">
        AND operation_log_id = #{operationLogId,jdbcType=BIGINT}
      </if>
    </where>
  </select>
</mapper>