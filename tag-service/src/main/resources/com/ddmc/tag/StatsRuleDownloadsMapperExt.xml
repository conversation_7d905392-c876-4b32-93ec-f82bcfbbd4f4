<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.tag.dao.tag.stats.RuleDownloadsDao">

    <select id="queryRuleDownloads" resultMap="BaseResultMap">
        <!--@mbg.generated 2020-08-06 20:07:35-->
        SELECT <include refid="Base_Column_List"/> FROM stats_rule_downloads
        <where>
            <if test="ruleId != null">
                AND rule_id = #{ruleId,jdbcType=BIGINT}
            </if>
            <if test="ruleName != null">
                AND rule_name like CONCAT('%',#{ruleName},'%')
            </if>
            <if test="categoryType != null">
                AND category_type = #{categoryType,jdbcType=TINYINT}
            </if>
        </where>
        order by id DESC limit #{start}, #{pageSize}
    </select>

</mapper>