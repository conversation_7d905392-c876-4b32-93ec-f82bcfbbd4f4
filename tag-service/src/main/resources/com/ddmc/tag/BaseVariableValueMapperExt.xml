<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.tag.dao.tag.base.BaseVariableValueDao">
    <select id="getValueByType" resultType="java.lang.String">
        select `value`
        from base_variable_value
        where `type` = #{type,jdbcType=TINYINT}
        and is_valid = true
    </select>

    <update id="compareAndUpdate">
        update base_variable_value
        set `value` = #{afterValue,jdbcType=VARCHAR}
        where `type` = #{type,jdbcType=TINYINT}
        and `value` = #{beforeValue,jdbcType=VARCHAR}
        and is_valid = true
    </update>
  
</mapper>