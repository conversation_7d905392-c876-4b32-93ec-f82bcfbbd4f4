<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.tag.dao.tag.schema.TagCategorySchemaDao">

    <update id="invalidateById">
        update tag_category_schema
        set is_valid = false,
        last_editor = #{userEmail,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}  and is_valid = true
    </update>

    <select id="querySchema" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tag_category_schema
        where is_valid = true
        <if test="isVisible != null">
            and is_visible = #{isVisible,jdbcType=TINYINT}
        </if>
        <if test="fieldType != null">
            and field_type = #{fieldType,jdbcType=TINYINT}
        </if>
        <if test="updateRate != null">
            and update_rate = #{updateRate,jdbcType=TINYINT}
        </if>
        <if test="fieldName != null">
            and field_name like CONCAT('%',#{fieldName},'%')
        </if>
        <if test="showName != null">
            and show_name like CONCAT('%',#{showName},'%')
        </if>
        <if test="categoryId != null">
            and category_id = #{categoryId,jdbcType=BIGINT}
        </if>
        order by id DESC limit #{skip},#{pageSize}
    </select>

    <select id="countQuerySchema" resultType="java.lang.Long">
        select count(1)
        from tag_category_schema
        where is_valid = true
        <if test="isVisible != null">
            and is_visible = #{isVisible,jdbcType=TINYINT}
        </if>
        <if test="fieldType != null">
            and field_type = #{fieldType,jdbcType=TINYINT}
        </if>
        <if test="updateRate != null">
            and update_rate = #{updateRate,jdbcType=TINYINT}
        </if>
        <if test="fieldName != null">
            and field_name like CONCAT('%',#{fieldName},'%')
        </if>
        <if test="showName != null">
            and show_name like CONCAT('%',#{showName},'%')
        </if>
        <if test="categoryId != null">
            and category_id = #{categoryId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="queryByFieldNames" resultType="java.lang.String">
        select field_name
        from tag_category_schema
        where is_valid = true
        and field_name in
        <foreach collection="fieldNames" item="fieldName" open="(" separator="," close=")">
            #{fieldName,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="querySchemaByFieldNames" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tag_category_schema
        where is_valid = true
        and field_name in
        <foreach collection="fieldNames" item="fieldName" open="(" separator="," close=")">
            #{fieldName,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="queryTagFieldNamesByUpdateRate" resultType="java.lang.String">
        select field_name
        from tag_category_schema
        where is_valid = true
        and update_rate = #{updateRate,jdbcType=TINYINT}
    </select>

    <select id="queryAllValidTags" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tag_category_schema
        where is_valid = true
    </select>

    <select id="queryValidByFieldNamesAndCreateTime" resultMap="BaseResultMap" >
        select <include refid="Base_Column_List"/>
        from tag_category_schema
        where is_valid = true
        and status = 1
        and create_time &lt; date_sub(now(),interval #{days} day)
    </select>

    <update id="updateStatusByIds">
        update tag_category_schema
        set status = #{status,jdbcType=INTEGER},
        status_update_time = NOW()
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
        and is_valid = true
    </update>

    <select id="queryValidTagsByStatus" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tag_category_schema
        where is_valid = true
        and status = #{status,jdbcType=INTEGER}
    </select>

    <select id="queryValidTagsByStatusAndIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tag_category_schema
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
        and is_valid = true
        and status = #{status,jdbcType=INTEGER}
    </select>

</mapper>