<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.tag.dao.tag.schema.TagCategoryFluctuationHistoryDao">
  <resultMap id="BaseResultMap" type="com.ddmc.tag.model.schema.TagCategoryFluctuationHistory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="schema_name" jdbcType="VARCHAR" property="schemaName" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="index_count" jdbcType="INTEGER" property="indexCount" />
    <result column="opt_date" jdbcType="VARCHAR" property="optDate" />
    <result column="batch" jdbcType="TINYINT" property="batch" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, schema_name, type, index_count, opt_date, batch, create_time, update_time
  </sql>

  <insert id="insert" parameterType="com.ddmc.tag.model.schema.TagCategoryFluctuationHistory">
    INSERT INTO tag_schema_fluctuation_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      <if test="schemaName != null">
        schema_name,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="indexCount != null">
        index_count,
      </if>
      <if test="optDate != null">
        opt_date,
      </if>
      <if test="batch != null">
        batch,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
      #{id,jdbcType=BIGINT},
      <if test="schemaName != null">
        #{schemaName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="indexCount != null">
        #{indexCount,jdbcType=INTEGER},
      </if>
      <if test="optDate != null">
        #{optDate,jdbcType=VARCHAR},
      </if>
      <if test="batch != null">
        #{batch,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="update" parameterType="com.ddmc.tag.model.schema.TagCategoryFluctuationHistory">
    UPDATE tag_schema_fluctuation_history
    <set>
      <if test="schemaName != null">
        schema_name = #{schemaName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="indexCount != null">
        index_count = #{indexCount,jdbcType=INTEGER},
      </if>
      <if test="optDate != null">
        opt_date = #{optDate,jdbcType=VARCHAR},
      </if>
      <if test="batch != null">
        batch = #{batch,jdbcType=TINYINT},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="insertList" parameterType="java.util.List">
    INSERT INTO tag_schema_fluctuation_history (
    schema_name, type, index_count, opt_date, batch, create_time, update_time
    ) VALUES
    <foreach collection="list" index="index" item="item" separator=",">
      (
      #{item.schemaName,jdbcType=VARCHAR}, #{item.type,jdbcType=TINYINT},
      #{item.indexCount,jdbcType=INTEGER}, #{item.optDate,jdbcType=VARCHAR},
      #{item.batch,jdbcType=TINYINT}, #{item.createTime,jdbcType=TIMESTAMP},
      #{item.updateTime,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>

  <insert id="saveOrUpdateBatch" parameterType="com.ddmc.tag.model.schema.TagCategoryFluctuationHistory">
    INSERT INTO tag_schema_fluctuation_history (
    schema_name, type, index_count, opt_date, batch
    ) VALUES
    <foreach collection="list" index="index" item="item" separator=",">
      (
      #{item.schemaName,jdbcType=VARCHAR}, #{item.type,jdbcType=TINYINT},
      #{item.indexCount,jdbcType=INTEGER}, #{item.optDate,jdbcType=VARCHAR},
      #{item.batch,jdbcType=TINYINT}
      )
    </foreach>
    ON DUPLICATE KEY UPDATE
    schema_name = VALUES (schema_name),
    type = VALUES (type),
    index_count = VALUES (index_count),
    opt_date = VALUES (opt_date),
    batch = VALUES (batch),
    update_time = NOW()
  </insert>

  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM tag_schema_fluctuation_history WHERE id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectBySchemaName" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM tag_schema_fluctuation_history WHERE schema_name = #{schemaName,jdbcType=VARCHAR}
  </select>

  <select id="selectBySchemaNames" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM tag_schema_fluctuation_history
    WHERE schema_name IN
    <foreach collection="schemaNames" item="schemaName" open="(" close=")" separator=",">
      #{schemaName,jdbcType=VARCHAR}
    </foreach>
  </select>

  <select id="selectCountByTypeAndOptDate" resultType="java.lang.Long">
    SELECT
      COUNT(1)
    FROM
      tag_schema_fluctuation_history
    WHERE
      type = #{type,jdbcType=TINYINT}
    AND
      opt_date = #{optDate,jdbcType=VARCHAR}
  </select>

  <select id="selectBySchemaNamesAndOptDate" resultMap="BaseResultMap" >
    SELECT
      <include refid="Base_Column_List"/>
    FROM
      tag_schema_fluctuation_history
    WHERE
      schema_name
    IN
    <foreach collection="schemaNames" item="schemaName" open="(" close=")" separator=",">
      #{schemaName,jdbcType=VARCHAR}
    </foreach>
    AND
      type = #{type,jdbcType=TINYINT}
    AND
      opt_date = #{optDate,jdbcType=VARCHAR}
  </select>

  <select id="selectLatestBatchToday" resultType="java.lang.Integer">
    SELECT
      MAX(batch)
    FROM
      tag_schema_fluctuation_history
    WHERE
      type = #{type,jdbcType=TINYINT}
    AND
      opt_date = #{optDate,jdbcType=VARCHAR}
  </select>

  <select id="selectByBatch" resultMap="BaseResultMap" >
    SELECT
      <include refid="Base_Column_List"/>
    FROM
      tag_schema_fluctuation_history
    WHERE
      schema_name
    IN
    <foreach collection="tags" item="schemaName" open="(" close=")" separator=",">
      #{schemaName,jdbcType=VARCHAR}
    </foreach>
    AND
      type = #{type,jdbcType=TINYINT}
    AND
      opt_date = #{optDate,jdbcType=VARCHAR}
    AND
      batch = #{batch,jdbcType=TINYINT}
  </select>

  <select id="selectLatestTwoBatchCountByTypeAndDateAndTag" resultType="java.lang.Integer">
    SELECT
      index_count
    FROM
      tag_schema_fluctuation_history
    WHERE
      type = #{type,jdbcType=TINYINT}
    AND
      schema_name = #{tag,jdbcType=VARCHAR}
    ORDER BY ID DESC
    LIMIT 2
  </select>

</mapper>