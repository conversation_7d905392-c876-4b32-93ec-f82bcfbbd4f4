<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.tag.dao.tag.rule.AlgoNeedsMapper">

    <resultMap id="BaseResultMap" type="com.ddmc.tag.model.rule.AlgoNeeds">
        <result property="id" column="id"/>
        <result property="productRuleId" column="product_rule_id"/>
        <result property="algoModelId" column="algo_model_id"/>
        <result property="algoModel" column="algo_model"/>
        <result property="lastUsageType" column="last_usage_type"/>
        <result property="adjustedScore" column="adjusted_score"/>
        <result property="algoUserRuleIds" column="algo_user_rule_ids"/>
        <result property="algoUserRuleType" column="algo_user_rule_type"/>
        <result property="creator" column="creator"/>
        <result property="updater" column="updater"/>
        <result property="contractor" column="contractor"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="status" column="status"/>
        <result property="readyTime" column="ready_time"/>
        <result property="userCnt" column="user_cnt"/>
        <result property="isValid" column="is_valid"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


    <sql id="Base_Column_List">
        <trim suffixOverrides=",">
            `id`,
            `product_rule_id`,
            `algo_model_id`,
            `algo_model`,
            `last_usage_type`,
            `adjusted_score`,
            `algo_user_rule_ids`,
            `algo_user_rule_type`,
            `creator`,
            `updater`,
            `contractor`,
            `start_time`,
            `end_time`,
            `status`,
            `ready_time`,
            `user_cnt`,
            `is_valid`,
            `create_time`,
            `update_time`,
        </trim>
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO `algo_needs`
        (
        <trim suffixOverrides=",">
            <if test="productRuleId != null">
                `product_rule_id`,
            </if>
            <if test="algoModelId != null">
                `algo_model_id`,
            </if>
            <if test="algoModel != null">
                `algo_model`,
            </if>
            <if test="lastUsageType != null">
                `last_usage_type`,
            </if>
            <if test="adjustedScore != null">
                `adjusted_score`,
            </if>
            <if test="algoUserRuleIds != null">
                `algo_user_rule_ids`,
            </if>
            <if test="algoUserRuleType != null">
                `algo_user_rule_type`,
            </if>
            <if test="creator != null">
                `creator`,
            </if>
            <if test="updater != null">
                `updater`,
            </if>
            <if test="contractor != null">
                `contractor`,
            </if>
            <if test="startTime != null">
                `start_time`,
            </if>
            <if test="endTime != null">
                `end_time`,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="readyTime != null">
                `ready_time`,
            </if>
            <if test="userCnt != null">
                `user_cnt`,
            </if>
            <if test="isValid != null">
                `is_valid`,
            </if>
        </trim>
        )
        VALUES
        (
        <trim suffixOverrides=",">
            <if test="productRuleId != null">
                #{productRuleId},
            </if>
            <if test="algoModelId != null">
                #{algoModelId},
            </if>
            <if test="algoModel != null">
                #{algoModel},
            </if>
            <if test="lastUsageType != null">
                #{lastUsageType},
            </if>
            <if test="adjustedScore != null">
                #{adjustedScore},
            </if>
            <if test="algoUserRuleIds != null">
                #{algoUserRuleIds},
            </if>
            <if test="algoUserRuleType != null">
                #{algoUserRuleType},
            </if>
            <if test="creator != null">
                #{creator},
            </if>
            <if test="updater != null">
                #{updater},
            </if>
            <if test="contractor != null">
                #{contractor},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="readyTime != null">
                #{readyTime},
            </if>
            <if test="userCnt != null">
                #{userCnt},
            </if>
            <if test="isValid != null">
                #{isValid},
            </if>
        </trim>
        )
    </insert>


    <update id="update">
        UPDATE `algo_needs`
        SET
        <trim suffixOverrides=",">
            <if test="productRuleId != null">
                `product_rule_id` = #{productRuleId},
            </if>
            <if test="algoModelId != null">
                `algo_model_id` = #{algoModelId},
            </if>
            <if test="algoModel != null and algoModel != ''">
                `algo_model` = #{algoModel},
            </if>
            <if test="lastUsageType != null">
                `last_usage_type` = #{lastUsageType},
            </if>
            <if test="adjustedScore != null">
                `adjusted_score` = #{adjustedScore},
            </if>
            <if test="algoUserRuleIds != null and algoUserRuleIds != ''">
                `algo_user_rule_ids` = #{algoUserRuleIds},
            </if>
            <if test="algoUserRuleType != null">
                `algo_user_rule_type` = #{algoUserRuleType},
            </if>
            <if test="creator != null and creator != ''">
                `creator` = #{creator},
            </if>
            <if test="updater != null and updater != ''">
                `updater` = #{updater},
            </if>
            <if test="contractor != null and contractor != ''">
                `contractor` = #{contractor},
            </if>
            <if test="startTime != null">
                `start_time` = #{startTime},
            </if>
            <if test="endTime != null">
                `end_time` = #{endTime},
            </if>
            <if test="status != null">
                `status` = #{status},
            </if>
            <if test="readyTime != null">
                `ready_time` = #{readyTime},
            </if>
            <if test="userCnt != null">
                `user_cnt` = #{userCnt},
            </if>
            <if test="isValid != null">
                `is_valid` = #{isValid},
            </if>
        </trim>
        WHERE id = #{id}
    </update>

    <select id="selectAllValid" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM algo_needs
        <where>
            is_valid = 1
            <if test="id != null">
                AND id = #{id,jdbcType=BIGINT}
            </if>
            <if test="productRuleId != null">
                AND product_rule_id = #{productRuleId,jdbcType=BIGINT}
            </if>
            <if test="userRuleId != null">
                AND algo_user_rule_ids LIKE CONCAT('%', #{userRuleId,jdbcType=VARCHAR}, '%')
            </if>
            <if test="algoModelId != null">
                AND algo_model_id = #{algoModelId,jdbcType=BIGINT}
            </if>
            <if test="needsStatus != null">
                AND status = #{needsStatus,jdbcType=TINYINT}
            </if>
            <if test="creator != null">
                AND creator LIKE CONCAT('%', #{creator,jdbcType=VARCHAR}, '%')
            </if>
        </where>
        ORDER BY create_time DESC LIMIT #{startIndex}, #{pageSize}
    </select>

    <select id="countAllValid" resultType="java.lang.Long">
        SELECT count(1) FROM algo_needs
        <where>
            is_valid = 1
            <if test="id != null">
                AND id = #{id,jdbcType=BIGINT}
            </if>
            <if test="productRuleId != null">
                AND product_rule_id = #{productRuleId,jdbcType=BIGINT}
            </if>
            <if test="userRuleId != null">
                AND algo_user_rule_ids LIKE CONCAT('%', #{userRuleId,jdbcType=VARCHAR}, '%')
            </if>
            <if test="algoModelId != null">
                AND algo_model_id = #{algoModelId,jdbcType=BIGINT}
            </if>
            <if test="needsStatus != null">
                AND status = #{needsStatus,jdbcType=TINYINT}
            </if>
            <if test="creator != null">
                AND creator LIKE CONCAT('%', #{creator,jdbcType=VARCHAR}, '%')
            </if>
        </where>
    </select>

    <update id="updateStatus">
        UPDATE algo_needs
        SET status = #{status,jdbcType=TINYINT},
        update_time = NOW()
        WHERE
        id = #{id,jdbcType=BIGINT}
    </update>

    <update id="invalid">
        UPDATE algo_needs
        SET is_valid = 0,
        update_time = NOW()
        WHERE
        id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByNeedStatuses" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM algo_needs
        <where>
            status in
            <foreach collection="needStatuses" item="needStatus" open="(" close=")" separator=",">
                #{needStatus}
            </foreach>
        </where>
    </select>

    <select id="selectByNeedStatusesAndUpdateTime" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM algo_needs
        <where>
            status in
            <foreach collection="needStatuses" item="needStatus" open="(" close=")" separator=",">
                #{needStatus}
            </foreach>
            and
            update_time &lt; #{updateTime}
        </where>
    </select>

    <select id="selectValidNeedById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM algo_needs
        <where>
            id = #{id,jdbcType=BIGINT}
            AND
            is_valid = 1
        </where>
    </select>

    <select id="selectValidNeedByProductRuleId" resultType="com.ddmc.tag.model.rule.AlgoNeeds">
        SELECT
        <include refid="Base_Column_List"/>
        FROM algo_needs
        <where>
            product_rule_id = #{productRuleId,jdbcType=BIGINT}
            AND
            is_valid = 1
        </where>
    </select>

</mapper>