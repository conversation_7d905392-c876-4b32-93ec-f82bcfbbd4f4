<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.tag.dao.tag.stats.UserWillLoadDao">
  <resultMap id="BaseResultMap" type="com.ddmc.tag.model.stats.UserWillLoad">
    <!--@mbg.generated 2020-05-29 16:30:31-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="month" jdbcType="VARCHAR" property="month" />
    <result column="reg_time_mark" jdbcType="BIGINT" property="regTimeMark" />
    <result column="user_id_mark" jdbcType="VARCHAR" property="userIdMark" />
    <result column="im_uid_mark" jdbcType="BIGINT" property="imUidMark" />
    <result column="last_load_time" jdbcType="TIMESTAMP" property="lastLoadTime" />
    <result column="is_valid" jdbcType="TINYINT" property="isValid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="type" jdbcType="TINYINT" property="type" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated 2020-05-29 16:30:31-->
    id, month, reg_time_mark, user_id_mark, im_uid_mark, last_load_time, is_valid, create_time, update_time,
    type
  </sql>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ddmc.tag.model.stats.UserWillLoad" useGeneratedKeys="true">
    <!--@mbg.generated 2020-05-29 16:30:31-->
    INSERT INTO stats_user_will_load
    <trim prefix="(" suffix=")" suffixOverrides=",">
      month,
      reg_time_mark,
      <if test="userIdMark != null">
        user_id_mark,
      </if>
      <if test="imUidMark != null">
        im_uid_mark,
      </if>
      <if test="lastLoadTime != null">
        last_load_time,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="type != null">
        type,
      </if>
    </trim>
    VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
      #{month,jdbcType=VARCHAR},
      #{regTimeMark,jdbcType=BIGINT},
      <if test="userIdMark != null">
        #{userIdMark,jdbcType=VARCHAR},
      </if>
      <if test="imUidMark != null">
        #{imUidMark,jdbcType=BIGINT},
      </if>
      <if test="lastLoadTime != null">
        #{lastLoadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <insert id="insertList" parameterType="java.util.List">
    <!--@mbg.generated 2020-05-29 16:30:31-->
    INSERT INTO stats_user_will_load (
      month, reg_time_mark, user_id_mark, im_uid_mark, last_load_time, is_valid, create_time, update_time,
      type
    ) VALUES 
    <foreach collection="list" index="index" item="item" separator=",">
      (
        #{item.month,jdbcType=VARCHAR}, #{item.regTimeMark,jdbcType=BIGINT}, #{item.userIdMark,jdbcType=VARCHAR}, #{item.imUidMark,jdbcType=BIGINT},
      #{item.lastLoadTime,jdbcType=TIMESTAMP}, #{item.isValid,jdbcType=TINYINT}, #{item.createTime,jdbcType=TIMESTAMP},
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.type,jdbcType=TINYINT}
      )
    </foreach>
  </insert>
  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated 2020-05-29 16:30:31-->
    SELECT <include refid="Base_Column_List" /> FROM stats_user_will_load WHERE id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectOne" parameterType="com.ddmc.tag.model.stats.UserWillLoad" resultMap="BaseResultMap">
    <!--@mbg.generated 2020-05-29 16:30:31-->
    SELECT <include refid="Base_Column_List" /> FROM stats_user_will_load
    <where>
      <if test="month != null">
        AND month = #{month,jdbcType=VARCHAR}
      </if>
      <if test="regTimeMark != null">
        AND reg_time_mark = #{regTimeMark,jdbcType=BIGINT}
      </if>
      <if test="userIdMark != null">
        AND user_id_mark = #{userIdMark,jdbcType=VARCHAR}
      </if>
      <if test="imUidMark != null">
        AND im_uid_mark = #{imUidMark,jdbcType=BIGINT}
      </if>
      <if test="lastLoadTime != null">
        AND last_load_time = #{lastLoadTime,jdbcType=TIMESTAMP}
      </if>
      <if test="isValid != null">
        AND is_valid = #{isValid,jdbcType=TINYINT}
      </if>
      <if test="createTime != null">
        AND create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        AND update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="type != null">
        AND type = #{type,jdbcType=TINYINT}
      </if>
      LIMIT 1
    </where>
  </select>
  <select id="selectList" parameterType="com.ddmc.tag.model.stats.UserWillLoad" resultMap="BaseResultMap">
    <!--@mbg.generated 2020-05-29 16:30:31-->
    SELECT <include refid="Base_Column_List" /> FROM stats_user_will_load
    <where>
      <if test="month != null">
        AND month = #{month,jdbcType=VARCHAR}
      </if>
      <if test="regTimeMark != null">
        AND reg_time_mark = #{regTimeMark,jdbcType=BIGINT}
      </if>
      <if test="userIdMark != null">
        AND user_id_mark = #{userIdMark,jdbcType=VARCHAR}
      </if>
      <if test="imUidMark != null">
        AND im_uid_mark = #{imUidMark,jdbcType=BIGINT}
      </if>
      <if test="lastLoadTime != null">
        AND last_load_time = #{lastLoadTime,jdbcType=TIMESTAMP}
      </if>
      <if test="isValid != null">
        AND is_valid = #{isValid,jdbcType=TINYINT}
      </if>
      <if test="createTime != null">
        AND create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        AND update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="type != null">
        AND type = #{type,jdbcType=TINYINT}
      </if>
    </where>
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    <!--@mbg.generated 2020-05-29 16:30:31-->
    SELECT <include refid="Base_Column_List" /> FROM stats_user_will_load where is_valid = true
  </select>
  <select id="count" parameterType="com.ddmc.tag.model.stats.UserWillLoad" resultType="java.lang.Long">
    <!--@mbg.generated 2020-05-29 16:30:31-->
    SELECT count(1) FROM stats_user_will_load
    <where>
      <if test="id != null">
        AND id=#{id,jdbcType=BIGINT}
      </if>
      <if test="month != null">
        AND month=#{month,jdbcType=VARCHAR}
      </if>
      <if test="regTimeMark != null">
        AND reg_time_mark=#{regTimeMark,jdbcType=BIGINT}
      </if>
      <if test="userIdMark != null">
        AND user_id_mark=#{userIdMark,jdbcType=VARCHAR}
      </if>
      <if test="imUidMark != null">
        AND im_uid_mark = #{imUidMark,jdbcType=BIGINT}
      </if>
      <if test="lastLoadTime != null">
        AND last_load_time=#{lastLoadTime,jdbcType=TIMESTAMP}
      </if>
      <if test="isValid != null">
        AND is_valid=#{isValid,jdbcType=TINYINT}
      </if>
      <if test="createTime != null">
        AND create_time=#{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        AND update_time=#{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="type != null">
        AND type=#{type,jdbcType=TINYINT}
      </if>
    </where>
  </select>
  <delete id="deleteById" parameterType="java.lang.Long">
    <!--@mbg.generated 2020-05-29 16:30:31-->
    DELETE FROM stats_user_will_load WHERE id = #{id,jdbcType=BIGINT}
  </delete>
  <update id="update" parameterType="com.ddmc.tag.model.stats.UserWillLoad">
    <!--@mbg.generated 2020-05-29 16:30:31-->
    UPDATE stats_user_will_load
    <set>
      <if test="month != null">
        month = #{month,jdbcType=VARCHAR},
      </if>
      <if test="regTimeMark != null">
        reg_time_mark = #{regTimeMark,jdbcType=BIGINT},
      </if>
      <if test="userIdMark != null">
        user_id_mark = #{userIdMark,jdbcType=VARCHAR},
      </if>
      <if test="imUidMark != null">
        im_uid_mark = #{imUidMark,jdbcType=BIGINT},
      </if>
      <if test="lastLoadTime != null">
        last_load_time = #{lastLoadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT}
  </update>
</mapper>