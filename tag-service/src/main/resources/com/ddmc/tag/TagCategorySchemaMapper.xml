<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.tag.dao.tag.schema.TagCategorySchemaDao">
  <resultMap id="BaseResultMap" type="com.ddmc.tag.model.schema.TagCategorySchema">
    <!--@mbg.generated 2020-04-27 17:44:17-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="show_order" jdbcType="TINYINT" property="showOrder" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="show_name" jdbcType="VARCHAR" property="showName" />
    <result column="field_type" jdbcType="TINYINT" property="fieldType" />
    <result column="schema_type" jdbcType="TINYINT" property="schemaType" />
    <result column="update_rate" jdbcType="TINYINT" property="updateRate" />

    <result column="station_dimension" jdbcType="TINYINT" property="stationDimension" />

    <result column="is_enum" jdbcType="TINYINT" property="isEnum" />
    <result column="enum_value" jdbcType="VARCHAR" property="enumValue" />
    <result column="is_visible" jdbcType="TINYINT" property="isVisible" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="is_valid" jdbcType="TINYINT" property="isValid" />
    <result column="last_editor" jdbcType="VARCHAR" property="lastEditor" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="ops_type" jdbcType="INTEGER" property="opsType" />
    <result column="auto_retrieval" jdbcType="TINYINT" property="autoRetrieval"/>
    <result column="tenant_id" jdbcType="TINYINT" property="tenantId"/>
    <result column="status" jdbcType="TINYINT" property="status"/>
    <result column="status_update_time" jdbcType="TIMESTAMP" property="statusUpdateTime"/>
    <result column="is_forever_valid" jdbcType="TINYINT" property="isForeverValid"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated 2020-04-27 17:44:17-->
    id, category_id, show_order, table_name, field_name, show_name, field_type, schema_type, update_rate, station_dimension,
    is_enum, enum_value, is_visible, note, is_valid, last_editor, create_time, update_time, 
    ops_type,auto_retrieval,tenant_id,status,status_update_time,is_forever_valid
  </sql>
  <insert id="insert" parameterType="com.ddmc.tag.model.schema.TagCategorySchema" useGeneratedKeys="true" keyProperty="id">
    <!--@mbg.generated 2020-04-27 17:44:17-->
    INSERT INTO tag_category_schema
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="showOrder != null">
        show_order,
      </if>
      <if test="tableName != null">
        table_name,
      </if>
      <if test="fieldName != null">
        field_name,
      </if>
      <if test="showName != null">
        show_name,
      </if>
      <if test="fieldType != null">
        field_type,
      </if>
      <if test="schemaType != null">
        schema_type,
      </if>
      <if test="updateRate != null">
        update_rate,
      </if>

      <if test="stationDimension != null">
        station_dimension,
      </if>

      <if test="isEnum != null">
        is_enum,
      </if>
      <if test="enumValue != null">
        enum_value,
      </if>
      <if test="isVisible != null">
        is_visible,
      </if>
      <if test="note != null">
        note,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
      <if test="lastEditor != null">
        last_editor,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="opsType != null">
        ops_type,
      </if>
    </trim>
    VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
      #{id,jdbcType=BIGINT},
      <if test="categoryId != null">
        #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="showOrder != null">
        #{showOrder,jdbcType=TINYINT},
      </if>
      <if test="tableName != null">
        #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="fieldName != null">
        #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="showName != null">
        #{showName,jdbcType=VARCHAR},
      </if>
      <if test="fieldType != null">
        #{fieldType,jdbcType=TINYINT},
      </if>
      <if test="schemaType != null">
        #{schemaType,jdbcType=TINYINT},
      </if>
      <if test="updateRate != null">
        #{updateRate,jdbcType=TINYINT},
      </if>

      <if test="stationDimension != null">
        #{stationDimension,jdbcType=TINYINT},
      </if>

      <if test="isEnum != null">
        #{isEnum,jdbcType=TINYINT},
      </if>
      <if test="enumValue != null">
        #{enumValue,jdbcType=VARCHAR},
      </if>
      <if test="isVisible != null">
        #{isVisible,jdbcType=TINYINT},
      </if>
      <if test="note != null">
        #{note,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=TINYINT},
      </if>
      <if test="lastEditor != null">
        #{lastEditor,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="opsType != null">
        #{opsType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <insert id="insertList" parameterType="java.util.List">
    <!--@mbg.generated 2020-04-27 17:44:17-->
    INSERT INTO tag_category_schema (
      id, category_id, show_order, table_name, field_name, show_name, field_type, schema_type, update_rate, station_dimension,
      is_enum, enum_value, is_visible, note, is_valid, last_editor, create_time, update_time, 
      ops_type
    ) VALUES 
    <foreach collection="list" index="index" item="item" separator=",">
      (
        #{item.id,jdbcType=BIGINT}, #{item.categoryId,jdbcType=BIGINT}, #{item.showOrder,jdbcType=TINYINT},
        #{item.tableName,jdbcType=VARCHAR}, #{item.fieldName,jdbcType=VARCHAR}, #{item.showName,jdbcType=VARCHAR}, 
        #{item.fieldType,jdbcType=TINYINT}, #{item.schemaType,jdbcType=TINYINT}, #{item.updateRate,jdbcType=TINYINT},
        #{item.stationDimension,jdbcType=TINYINT}, #{item.isEnum,jdbcType=TINYINT},
        #{item.enumValue,jdbcType=VARCHAR}, #{item.isVisible,jdbcType=TINYINT}, #{item.note,jdbcType=VARCHAR}, 
        #{item.isValid,jdbcType=TINYINT}, #{item.lastEditor,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.opsType,jdbcType=INTEGER}
      )
    </foreach>
  </insert>
  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated 2020-04-27 17:44:17-->
    SELECT <include refid="Base_Column_List"/> FROM tag_category_schema WHERE id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectOne" parameterType="com.ddmc.tag.model.schema.TagCategorySchema" resultMap="BaseResultMap">
    <!--@mbg.generated 2020-04-27 17:44:17-->
    SELECT <include refid="Base_Column_List"/> FROM tag_category_schema
    <where>
      <if test="categoryId != null">
        AND category_id = #{categoryId,jdbcType=BIGINT}
      </if>
      <if test="showOrder != null">
        AND show_order = #{showOrder,jdbcType=TINYINT}
      </if>
      <if test="tableName != null">
        AND table_name = #{tableName,jdbcType=VARCHAR}
      </if>
      <if test="fieldName != null">
        AND field_name = #{fieldName,jdbcType=VARCHAR}
      </if>
      <if test="showName != null">
        AND show_name = #{showName,jdbcType=VARCHAR}
      </if>
      <if test="fieldType != null">
        AND field_type = #{fieldType,jdbcType=TINYINT}
      </if>
      <if test="schemaType != null">
        AND schema_type = #{schemaType,jdbcType=TINYINT}
      </if>
      <if test="updateRate != null">
        AND update_rate = #{updateRate,jdbcType=TINYINT}
      </if>

      <if test="stationDimension != null">
        AND station_dimension = #{stationDimension,jdbcType=TINYINT}
      </if>

      <if test="isEnum != null">
        AND is_enum = #{isEnum,jdbcType=TINYINT}
      </if>
      <if test="enumValue != null">
        AND enum_value = #{enumValue,jdbcType=VARCHAR}
      </if>
      <if test="isVisible != null">
        AND is_visible = #{isVisible,jdbcType=TINYINT}
      </if>
      <if test="note != null">
        AND note = #{note,jdbcType=VARCHAR}
      </if>
      <if test="isValid != null">
        AND is_valid = #{isValid,jdbcType=TINYINT}
      </if>
      <if test="lastEditor != null">
        AND last_editor = #{lastEditor,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        AND create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        AND update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="opsType != null">
        AND ops_type = #{opsType,jdbcType=INTEGER}
      </if>
      LIMIT 1
    </where>
  </select>
  <select id="selectList" parameterType="com.ddmc.tag.model.schema.TagCategorySchema" resultMap="BaseResultMap">
    <!--@mbg.generated 2020-04-27 17:44:17-->
    SELECT <include refid="Base_Column_List"/> FROM tag_category_schema
    <where>
      <if test="categoryId != null">
        AND category_id = #{categoryId,jdbcType=BIGINT}
      </if>
      <if test="showOrder != null">
        AND show_order = #{showOrder,jdbcType=TINYINT}
      </if>
      <if test="tableName != null">
        AND table_name = #{tableName,jdbcType=VARCHAR}
      </if>
      <if test="fieldName != null">
        AND field_name = #{fieldName,jdbcType=VARCHAR}
      </if>
      <if test="showName != null">
        AND show_name = #{showName,jdbcType=VARCHAR}
      </if>
      <if test="fieldType != null">
        AND field_type = #{fieldType,jdbcType=TINYINT}
      </if>
      <if test="schemaType != null">
        AND schema_type = #{schemaType,jdbcType=TINYINT}
      </if>
      <if test="updateRate != null">
        AND update_rate = #{updateRate,jdbcType=TINYINT}
      </if>

      <if test="stationDimension != null">
        AND station_dimension = #{stationDimension,jdbcType=TINYINT}
      </if>

      <if test="isEnum != null">
        AND is_enum = #{isEnum,jdbcType=TINYINT}
      </if>
      <if test="enumValue != null">
        AND enum_value = #{enumValue,jdbcType=VARCHAR}
      </if>
      <if test="isVisible != null">
        AND is_visible = #{isVisible,jdbcType=TINYINT}
      </if>
      <if test="note != null">
        AND note = #{note,jdbcType=VARCHAR}
      </if>
      <if test="isValid != null">
        AND is_valid = #{isValid,jdbcType=TINYINT}
      </if>
      <if test="lastEditor != null">
        AND last_editor = #{lastEditor,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        AND create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        AND update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="opsType != null">
        AND ops_type = #{opsType,jdbcType=INTEGER}
      </if>
    </where>
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    <!--@mbg.generated 2020-04-27 17:44:17-->
    SELECT <include refid="Base_Column_List"/> FROM tag_category_schema
  </select>
  <select id="count" parameterType="com.ddmc.tag.model.schema.TagCategorySchema" resultType="java.lang.Long">
    <!--@mbg.generated 2020-04-27 17:44:17-->
    SELECT count(1) FROM tag_category_schema
    <where>
      <if test="id != null">
        AND id=#{id,jdbcType=BIGINT}
      </if>
      <if test="categoryId != null">
        AND category_id=#{categoryId,jdbcType=BIGINT}
      </if>
      <if test="showOrder != null">
        AND show_order=#{showOrder,jdbcType=TINYINT}
      </if>
      <if test="tableName != null">
        AND table_name=#{tableName,jdbcType=VARCHAR}
      </if>
      <if test="fieldName != null">
        AND field_name=#{fieldName,jdbcType=VARCHAR}
      </if>
      <if test="showName != null">
        AND show_name=#{showName,jdbcType=VARCHAR}
      </if>
      <if test="fieldType != null">
        AND field_type=#{fieldType,jdbcType=TINYINT}
      </if>
      <if test="schemaType != null">
        AND schema_type=#{schemaType,jdbcType=TINYINT}
      </if>
      <if test="updateRate != null">
        AND update_rate=#{updateRate,jdbcType=TINYINT}
      </if>

      <if test="stationDimension != null">
        AND station_dimension=#{stationDimension,jdbcType=TINYINT}
      </if>

      <if test="isEnum != null">
        AND is_enum=#{isEnum,jdbcType=TINYINT}
      </if>
      <if test="enumValue != null">
        AND enum_value=#{enumValue,jdbcType=VARCHAR}
      </if>
      <if test="isVisible != null">
        AND is_visible=#{isVisible,jdbcType=TINYINT}
      </if>
      <if test="note != null">
        AND note=#{note,jdbcType=VARCHAR}
      </if>
      <if test="isValid != null">
        AND is_valid=#{isValid,jdbcType=TINYINT}
      </if>
      <if test="lastEditor != null">
        AND last_editor=#{lastEditor,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        AND create_time=#{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        AND update_time=#{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="opsType != null">
        AND ops_type=#{opsType,jdbcType=INTEGER}
      </if>
    </where>
  </select>
  <delete id="deleteById" parameterType="java.lang.Long">
    <!--@mbg.generated 2020-04-27 17:44:17-->
    DELETE FROM tag_category_schema WHERE id = #{id,jdbcType=BIGINT}
  </delete>
  <update id="update" parameterType="com.ddmc.tag.model.schema.TagCategorySchema">
    <!--@mbg.generated 2020-04-27 17:44:17-->
    UPDATE tag_category_schema
    <set>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="showOrder != null">
        show_order = #{showOrder,jdbcType=TINYINT},
      </if>
      <if test="tableName != null">
        table_name = #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="fieldName != null">
        field_name = #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="showName != null">
        show_name = #{showName,jdbcType=VARCHAR},
      </if>
      <if test="fieldType != null">
        field_type = #{fieldType,jdbcType=TINYINT},
      </if>
      <if test="schemaType != null">
        schema_type = #{schemaType,jdbcType=TINYINT},
      </if>
      <if test="updateRate != null">
        update_rate = #{updateRate,jdbcType=TINYINT},
      </if>

      <if test="stationDimension != null">
        station_dimension = #{stationDimension,jdbcType=TINYINT},
      </if>

      <if test="isEnum != null">
        is_enum = #{isEnum,jdbcType=TINYINT},
      </if>
      <if test="enumValue != null">
        enum_value = #{enumValue,jdbcType=VARCHAR},
      </if>
      <if test="isVisible != null">
        is_visible = #{isVisible,jdbcType=TINYINT},
      </if>
      <if test="note != null">
        note = #{note,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=TINYINT},
      </if>
      <if test="lastEditor != null">
        last_editor = #{lastEditor,jdbcType=VARCHAR},
      </if>
      <if test="opsType != null">
        ops_type = #{opsType,jdbcType=INTEGER},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT}
  </update>
</mapper>