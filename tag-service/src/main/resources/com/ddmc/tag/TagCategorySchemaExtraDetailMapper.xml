<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.tag.dao.tag.schema.TagCategorySchemaExtraDetailDao">
  <resultMap id="BaseResultMap" type="com.ddmc.tag.model.schema.TagCategorySchemaExtraDetail">
    <!--@mbg.generated 2020-04-27 17:44:17-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tag_schema_id" jdbcType="BIGINT" property="tagSchemaId" />
    <result column="extra_id" jdbcType="BIGINT" property="extraId" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="field_show_name" jdbcType="VARCHAR" property="fieldShowName" />
    <result column="field_desc" jdbcType="VARCHAR" property="fieldDesc" />
    <result column="coverage_rate" jdbcType="VARCHAR" property="coverageRate" />
    <result column="coverage_count" jdbcType="BIGINT" property="coverageCount" />
    <result column="sync_method" jdbcType="TINYINT" property="syncMethod" />
    <result column="number_of_rule_configurations" jdbcType="BIGINT" property="numberOfRuleConfigurations"/>
    <result column="number_of_downstream_calls" jdbcType="BIGINT" property="numberOfDownstreamCalls"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, tag_schema_id, extra_id, field_name, field_show_name, field_desc, coverage_rate, coverage_count, sync_method, number_of_rule_configurations, number_of_downstream_calls, create_time, update_time
  </sql>

  <insert id="insert" parameterType="com.ddmc.tag.model.schema.TagCategorySchemaExtraDetail">
    INSERT INTO tag_category_schema_extra_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      <if test="tagSchemaId != null">
        tag_schema_id,
      </if>
      <if test="extraId != null">
        extra_id,
      </if>
      <if test="fieldName != null">
        field_name,
      </if>
      <if test="fieldShowName != null">
        field_show_name,
      </if>
      <if test="fieldDesc != null">
        field_desc,
      </if>
      <if test="coverageRate != null">
        coverage_rate,
      </if>
      <if test="coverageCount != null">
        coverage_count,
      </if>
      <if test="syncMethod != null">
        sync_method,
      </if>
      <if test="numberOfRuleConfigurations != null">
        number_of_rule_configurations,
      </if>
      <if test="numberOfDownstreamCalls != null">
        number_of_downstream_calls,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
      #{id,jdbcType=BIGINT},
      <if test="tagSchemaId != null">
        #{tagSchemaId,jdbcType=BIGINT},
      </if>
      <if test="extraId != null">
        #{extraId,jdbcType=BIGINT},
      </if>
      <if test="fieldName != null">
        #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="fieldShowName != null">
        #{fieldShowName,jdbcType=VARCHAR},
      </if>
      <if test="fieldDesc != null">
        #{fieldDesc,jdbcType=VARCHAR},
      </if>
      <if test="coverageRate != null">
        #{coverageRate,jdbcType=VARCHAR},
      </if>
      <if test="coverageCount != null">
        #{coverageCount,jdbcType=BIGINT},
      </if>
      <if test="syncMethod != null">
        #{syncMethod,jdbcType=TINYINT},
      </if>
      <if test="numberOfRuleConfigurations != null">
        #{numberOfRuleConfigurations},
      </if>
      <if test="numberOfDownstreamCalls != null">
        #{numberOfDownstreamCalls},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="update" parameterType="com.ddmc.tag.model.schema.TagCategorySchemaExtraDetail">
    UPDATE tag_category_schema_extra_detail
    <set>
      <if test="tagSchemaId != null">
        tag_schema_id = #{tagSchemaId,jdbcType=BIGINT},
      </if>
      <if test="extraId != null">
        extra_id = #{extraId,jdbcType=BIGINT},
      </if>
      <if test="fieldName != null">
        field_name = #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="fieldShowName != null">
        field_show_name = #{fieldShowName,jdbcType=VARCHAR},
      </if>
      <if test="fieldDesc != null">
        field_desc = #{fieldDesc,jdbcType=VARCHAR},
      </if>
      <if test="coverageRate != null">
        coverage_rate = #{coverageRate,jdbcType=VARCHAR},
      </if>
      <if test="coverageCount != null">
        coverage_count = #{coverageCount,jdbcType=BIGINT},
      </if>
      <if test="syncMethod != null">
        sync_method = #{syncMethod,jdbcType=TINYINT},
      </if>
      <if test="numberOfRuleConfigurations != null">
        number_of_rule_configurations = #{numberOfRuleConfigurations},
      </if>
      <if test="numberOfDownstreamCalls != null">
        number_of_downstream_calls = #{numberOfDownstreamCalls},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="insertList" parameterType="java.util.List">
    INSERT INTO tag_category_schema_extra_detail (
    tag_schema_id, extra_id, field_name, field_show_name, field_desc, coverage_rate, coverage_count,
    sync_method, number_of_rule_configurations, number_of_downstream_calls, create_time, update_time
    ) VALUES
    <foreach collection="list" index="index" item="item" separator=",">
      (
      #{item.tagSchemaId,jdbcType=BIGINT}, #{item.extraId,jdbcType=BIGINT},
      #{item.fieldName,jdbcType=VARCHAR}, #{item.fieldShowName,jdbcType=VARCHAR},
      #{item.fieldDesc,jdbcType=VARCHAR}, #{item.coverageRate,jdbcType=VARCHAR},
      #{item.coverageCount,jdbcType=VARCHAR}, #{item.syncMethod,jdbcType=TINYINT},
      #{item.numberOfRuleConfigurations}, #{item.numberOfDownstreamCalls},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>

  <insert id="saveOrUpdateBatch" parameterType="com.ddmc.tag.model.schema.TagCategorySchemaExtraDetail">
    INSERT INTO tag_category_schema_extra_detail (
    tag_schema_id, extra_id, field_name, field_show_name, field_desc, coverage_rate, coverage_count,
    sync_method,number_of_rule_configurations, number_of_downstream_calls
    ) VALUES
    <foreach collection="list" index="index" item="item" separator=",">
      (
      #{item.tagSchemaId,jdbcType=BIGINT}, #{item.extraId,jdbcType=BIGINT},
      #{item.fieldName,jdbcType=VARCHAR}, #{item.fieldShowName,jdbcType=VARCHAR},
      #{item.fieldDesc,jdbcType=VARCHAR}, #{item.coverageRate,jdbcType=VARCHAR},
      #{item.coverageCount,jdbcType=VARCHAR}, #{item.syncMethod,jdbcType=TINYINT},
      #{item.numberOfRuleConfigurations}, #{item.numberOfDownstreamCalls}
      )
    </foreach>
    ON DUPLICATE KEY UPDATE
    extra_id = VALUES (extra_id),
    field_name = VALUES (field_name),
    field_show_name = VALUES (field_show_name),
    field_desc = VALUES (field_desc),
    coverage_rate = VALUES (coverage_rate),
    coverage_count = VALUES (coverage_count),
    sync_method = VALUES (sync_method),
    number_of_rule_configurations = VALUES(number_of_rule_configurations),
    number_of_downstream_calls = VALUES(number_of_downstream_calls),
    update_time = NOW()
  </insert>

  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM tag_category_schema_extra_detail WHERE id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectBySchemaId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM tag_category_schema_extra_detail WHERE tag_schema_id = #{schemaId,jdbcType=BIGINT}
  </select>

  <select id="selectBySchemaIds" parameterType="java.lang.Long" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM tag_category_schema_extra_detail
    WHERE tag_schema_id IN
    <foreach collection="schemaIds" item="schemaId" open="(" close=")" separator=",">
      #{schemaId,jdbcType=BIGINT}
    </foreach>
  </select>

  <select id="selectBySchemaIdsAndSyncMethod" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM tag_category_schema_extra_detail
    WHERE tag_schema_id IN
    <foreach collection="schemaIds" item="schemaId" open="(" close=")" separator=",">
      #{schemaId,jdbcType=BIGINT}
    </foreach>
    AND sync_method = #{syncMethod,jdbcType=TINYINT}
  </select>

</mapper>