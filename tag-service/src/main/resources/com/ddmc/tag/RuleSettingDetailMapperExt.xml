<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.tag.dao.tag.rule.RuleSettingDetailDao">
    <update id="inValidByRuleId">
        UPDATE rule_setting_detail
        SET is_valid = false,
        update_time = NOW(),
        last_editor = #{lastEditor,jdbcType=VARCHAR}
        WHERE rule_id = #{ruleId,jdbcType=BIGINT} AND is_valid = true
    </update>

    <update id="inValidByRuleIds">
        UPDATE rule_setting_detail
        SET is_valid = false,
        update_time = NOW(),
        last_editor = #{lastEditor,jdbcType=VARCHAR}
        WHERE rule_id in
        <foreach collection="ruleIds" item="ruleId" open="(" close=")" separator=",">
            #{ruleId,jdbcType=BIGINT}
        </foreach>
        AND is_valid = true
    </update>
    
    <select id="countBySchemaId" resultType="java.lang.Integer">
        select count(1)
        from rule_setting_detail
        where is_valid = true
        and schema_id = #{schemaId, jdbcType=BIGINT}
    </select>

    <update id="updateList">
        <foreach collection="ruleSettingDetailList" item="detail" index="index" open="" close="" separator=";">
            update rule_setting_detail
            <trim prefix="SET" suffixOverrides=",">
                <if test="detail.ruleId != null ">rule_id = #{detail.ruleId},</if>
                <if test="detail.levelCode != null">level_code = #{detail.levelCode},</if>
                <if test="detail.logic != null">logic = #{detail.logic},</if>
                <if test="detail.categoryId != null">category_id = #{detail.categoryId},</if>
                <if test="detail.schemaId != null">schema_id = #{detail.schemaId},</if>
                <if test="detail.fieldName != null">field_name = #{detail.fieldName},</if>
                <if test="detail.compare != null">compare = #{detail.compare},</if>
                <if test="detail.valueStart != null">value_start = #{detail.valueStart},</if>
                <if test="detail.valueEnd != null">value_end = #{detail.valueEnd},</if>
                <if test="detail.ruleDrools != null">rule_drools = #{detail.ruleDrools},</if>
                <if test="detail.ruleDsl != null">rule_dsl = #{detail.ruleDsl},</if>
                <if test="detail.isValid != null">is_valid = #{detail.isValid},</if>
                <if test="detail.lastEditor != null">last_editor = #{detail.lastEditor},</if>
                <if test="detail.createTime != null">create_time = #{detail.createTime},</if>
                <if test="detail.updateTime != null">update_time = #{detail.updateTime},</if>
            </trim>
            where id= #{detail.id}
        </foreach>
    </update>

    <select id="selectByRuleIds" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM rule_setting_detail
        WHERE rule_id IN
        <foreach collection="ruleIds" item="ruleId" open="(" close=")" separator=",">
            #{ruleId,jdbcType=BIGINT}
        </foreach>
        AND is_valid = true
    </select>

    <select id="countFieldNameByRule" resultType="com.ddmc.tag.model.bo.FieldRuleConfiguration">
        select field_name as name,count(*) as num
        from rule_setting_detail
        where is_valid = true AND field_name in
        <foreach collection="fieldList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by field_name
    </select>

    <select id="selectValidByTagIds" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM rule_setting_detail
        WHERE is_valid = true
        AND schema_id = #{tagId,jdbcType=BIGINT}
    </select>

</mapper>