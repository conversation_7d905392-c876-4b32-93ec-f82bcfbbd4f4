<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.tag.dao.tag.log.AdminOperationLogDao">

    <select id="queryOperationLog" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM admin_operation_log
        <where>
            <if test="objectType != null">
                AND object_type = #{objectType,jdbcType=INTEGER}
            </if>
            <if test="objectDataId != null">
                AND object_data_id = #{objectDataId,jdbcType=BIGINT}
            </if>
        </where>
        ORDER BY create_time DESC LIMIT #{start}, #{pageSize}
    </select>

    <select id="queryLatestRecordByTypeAndRuleIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM admin_operation_log RIGHT JOIN (
        SELECT object_data_id rid, MAX(id) mid FROM admin_operation_log
        <where>
            object_data_id IN
            <foreach collection="ruleIds" item="ruleId" open="(" close=")" separator=",">
                #{ruleId,jdbcType=BIGINT}
            </foreach>
            <if test="operationType != null">
                AND operation_type = #{operationType,jdbcType=INTEGER}
            </if>
            <if test="objectType != null">
                AND object_type = #{objectType,jdbcType=INTEGER}
            </if>
        </where>

        GROUP BY object_data_id ) tmax
        ON admin_operation_log.id = tmax.mid
    </select>

</mapper>