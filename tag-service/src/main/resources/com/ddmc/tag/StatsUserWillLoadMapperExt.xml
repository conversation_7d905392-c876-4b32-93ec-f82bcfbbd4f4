<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.tag.dao.tag.stats.UserWillLoadDao">
  <select id="findByLastStatTime" resultMap="BaseResultMap">
      select <include refid="Base_Column_List"/>
      from stats_user_will_load
      where #{maxLastLoadTime} > last_load_time
      AND is_valid = true
  </select>

    <update id="batchInit"  parameterType="com.ddmc.tag.model.stats.UserWillLoad">
        <foreach collection="userWillLoadList" item="userWillLoad" index="index" open="" close="" separator=";">
            update stats_user_will_load
            <set>
                reg_time_mark = #{userWillLoad.regTimeMark,jdbcType=BIGINT},
                user_id_mark = #{userWillLoad.userIdMark,jdbcType=VARCHAR},
                im_uid_mark = #{userWillLoad.imUidMark,jdbcType=BIGINT},
                last_load_time = #{userWillLoad.lastLoadTime,jdbcType=TIMESTAMP}
            </set>
            where `month` = #{userWillLoad.month,jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="batchInitById"  parameterType="com.ddmc.tag.model.stats.UserWillLoad">
        <foreach collection="userWillLoadList" item="userWillLoad" index="index" open="" close="" separator=";">
            update stats_user_will_load
            <set>
                reg_time_mark = #{userWillLoad.regTimeMark,jdbcType=BIGINT},
                user_id_mark = #{userWillLoad.userIdMark,jdbcType=VARCHAR},
                im_uid_mark = #{userWillLoad.imUidMark,jdbcType=BIGINT},
                last_load_time = #{userWillLoad.lastLoadTime,jdbcType=TIMESTAMP},
                is_valid = #{userWillLoad.isValid,jdbcType=TINYINT}
            </set>
            where id = #{userWillLoad.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="findByType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from stats_user_will_load
        where `type` = #{type, jdbcType=TINYINT}
        and is_valid = true
    </select>

    <select id="findByTypeAndMonth" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from stats_user_will_load
        where `type` = #{type,jdbcType=TINYINT}
        and `month` = #{month,jdbcType=VARCHAR}
        and is_valid = true
    </select>
</mapper>