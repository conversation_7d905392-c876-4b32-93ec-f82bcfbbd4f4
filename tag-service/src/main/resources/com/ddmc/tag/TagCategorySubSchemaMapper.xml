<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.tag.dao.tag.schema.TagCategorySubSchemaDao">


    <resultMap id="BaseResultMap" type="com.ddmc.tag.model.schema.TagCategorySubSchema">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="tag_schema_id" jdbcType="BIGINT" property="tagSchemaId" />
        <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
        <result column="show_name" jdbcType="VARCHAR" property="showName" />
        <result column="field_type" jdbcType="TINYINT" property="fieldType" />
        <result column="ops_type" jdbcType="INTEGER" property="opsType" />
        <result column="is_enum" jdbcType="TINYINT" property="isEnum" />
        <result column="enum_value" jdbcType="VARCHAR" property="enumValue" />
        <result column="note" jdbcType="VARCHAR" property="note" />
        <result column="is_valid" jdbcType="TINYINT" property="isValid" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="updater" jdbcType="VARCHAR" property="updater" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, tag_schema_id, field_name, show_name, field_type, ops_type, is_enum, enum_value, note, is_valid, creator, updater, create_time, update_time
    </sql>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM tag_category_sub_schema WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectBySchemaId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tag_category_sub_schema
        WHERE tag_schema_id = #{schemaId,jdbcType=BIGINT} and is_valid = true
    </select>

    <select id="selectBySchemaIds" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tag_category_sub_schema
        WHERE is_valid = true and tag_schema_id IN
        <foreach collection="schemaIds" item="schemaId" open="(" close=")" separator=",">
            #{schemaId,jdbcType=BIGINT}
        </foreach>
    </select>

    <update id="invalidateByTagSchemaId">
        UPDATE tag_category_sub_schema
        SET
        is_valid = false, updater = #{userEmail,jdbcType=VARCHAR}
        WHERE
        tag_schema_id = #{tagSchemaId,jdbcType=BIGINT} and is_valid = true
    </update>

    <insert id="insertList" parameterType="java.util.List">
        INSERT INTO tag_category_sub_schema (
        tag_schema_id, field_name, show_name, field_type, ops_type, is_enum, enum_value, note, is_valid, creator, updater, create_time, update_time
        ) VALUES
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.tagSchemaId,jdbcType=BIGINT},
            #{item.fieldName,jdbcType=VARCHAR},
            #{item.showName,jdbcType=VARCHAR},
            #{item.fieldType,jdbcType=TINYINT},
            #{item.opsType,jdbcType=INTEGER},
            #{item.isEnum,jdbcType=TINYINT},
            #{item.enumValue,jdbcType=VARCHAR},
            #{item.note,jdbcType=VARCHAR},
            #{item.isValid,jdbcType=TINYINT},
            #{item.creator,jdbcType=VARCHAR},
            #{item.updater,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <insert id="insertOrUpdateById" parameterType="java.util.List">
        insert into tag_category_sub_schema(
        id, tag_schema_id, field_name, show_name, field_type, ops_type, is_enum, enum_value, note, is_valid, creator, updater, create_time, update_time
        )VALUES
        <foreach collection ="list" item="subSchema" index= "index" separator =",">
            (
            #{subSchema.id}, #{subSchema.tagSchemaId}, #{subSchema.fieldName},
            #{subSchema.showName}, #{subSchema.fieldType}, #{subSchema.opsType},
            #{subSchema.isEnum}, #{subSchema.enumValue}, #{subSchema.note},
            #{subSchema.isValid}, #{subSchema.creator}, #{subSchema.updater},
            #{subSchema.createTime}, #{subSchema.updateTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        tag_schema_id = VALUES(tag_schema_id),
        field_name = VALUES(field_name),
        show_name = VALUES(show_name),
        field_type = VALUES(field_type),
        ops_type = VALUES(ops_type),
        is_enum = VALUES(is_enum),
        enum_value = VALUES(enum_value),
        note = VALUES(note),
        is_valid = VALUES(is_valid),
        creator = VALUES(creator),
        updater = VALUES(updater),
        create_time = VALUES(create_time),
        update_time = VALUES(update_time)
    </insert>

</mapper>