package com.ddmc.tag.config;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/8/10
 */
@Component
public class ApolloConfig {

    //人群圈选大数据集免审角色列表
    @ApolloJsonValue("${rule.bigData.examine.immunity.roleIdList:[]}")
    public Set<String> examineImmunityRoleIdList;

    @Value("${spring.application.name}")
    public String appName;

    @Value("${alpha.bi.secretKey}")
    public String alphaBiSecreteKey;

    @Value("${realTimeWarehous.kafka.topic:}")
    public String realTimeWarehouseKafkaTopic;

    @Value("${realTimeWarehouse.userBatchSize:500}")
    public int realTimeWarehouseUserBatchSize;

    @Value("${realTimeWarehouse.kafka.switch:true}")
    public boolean realTimeWarehouseKafkaSwitch;

    @Value("${kafka.producer.success.log.switch:false}")
    public boolean kafkaProducerSuccessLogSwitch;

    @Value("${refreshRuleDslAndSql.switch:true}")
    public boolean refreshRuleDslAndSqlSwitch;

    @Value("${refresh.drools.msg.switch:false}")
    public boolean refreshDroolsMsgSwitch;

    @Value("${execute.check.count.switch:true}")
    public boolean executeCheckCountSwitch;

    @Value("${kepler.kafka.switch:true}")
    public boolean keplerKafkaSwitch;

    @Value("${kepler.userBatchSize:500}")
    public int keplerUserBatchSize;

    @Value("${kepler.kafka.topic:}")
    public String keplerKafkaTopic;

    @Value("${jadot.app.id:a000002}")
    public String jadotAppId;

    @Value("${kepler.kafka.restart.rule.switch:false}")
    public boolean keplerKafkaRestartRuleSwitch;

    @ApolloJsonValue("${rule.setting.product.union.redis.unlink.gray.ruleIds:[]}")
    public Set<Long> redisUnlinkGrayRuleIds;

    // 个推外采标签基础人群包策略id
    @Value("${external.user.rule.id:165784}")
    public Long externalUserRuleId;

    // 外采标签mock设备id百分比 0-1之间，最多两位小数
    @Value("${external.tag.mock.oaid.percent:0.5}")
    public String externalTagMockOaidPercentString;

    // 查询用户设备id为空，随机生成设备id进行mock开关
    @Value("${external.mock.device.id.when.blank.switch:true}")
    public boolean externalMockDeviceIdWhenBlankSwitch;

    // 真实oaid通过个推给出的有效MD5设备id进行mock开关
    @Value("${external.true.oaid.mock.getui.deviceIdMd5.switch:true}")
    public boolean externalTureOaidMockGetuiDeviceMd5Switch;

    // 掺入oaid通过个推给出的有效MD5设备id进行mock开关
    @Value("${external.mock.oaid.mock.getui.deviceIdMd5.switch:false}")
    public boolean externalMockOaidMockGetuiDeviceMd5Switch;

    // 外采标签kafka消息topic
    @Value("${external.tag.collect.kafka.topic:}")
    public String externalTagCollectKafkaTopic;

    // 分仓商品策略落非分仓逆向开关
    @Value("${scene.bystation.save.default.reverse.switch:true}")
    public boolean sceneBystationSaveDefaultReversSwitch;

}
