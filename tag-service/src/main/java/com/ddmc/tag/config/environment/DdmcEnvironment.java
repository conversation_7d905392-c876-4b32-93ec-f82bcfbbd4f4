package com.ddmc.tag.config.environment;

import com.ddmc.middleware.foundation.Foundation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Component
@Slf4j
public class DdmcEnvironment {

    private static volatile DdmcEnvEnums currentEnv = null;

    public static DdmcEnvEnums getEnv() {
        if (currentEnv == null) {
            synchronized (DdmcEnvironment.class) {

                if (currentEnv == null) {
                    String env = Foundation.info().env().name();

                    log.info("DdmcEnvironment-getEnv-key:{}", env);

                    DdmcEnvEnums target = Arrays.stream(DdmcEnvEnums.values()).filter(item -> item.getCode().equals(env))
                            .findFirst().orElse(DdmcEnvEnums.DEFAULT);

                    log.info("DdmcEnvironment-getEnv-value:{}", target);

                    currentEnv = target;

                    return target;
                }
            }
        }
        return currentEnv;
    }

}
