package com.ddmc.tag.config.message.kafka;

import com.ddmc.tag.message.kafka.KafkaConsumer;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.ByteArrayDeserializer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;

import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableKafka
public class KafkaConsumerConfig {

    @Value("${kepler.sql.rule.kafka.consumerServers:}")
    private String sqlRuleExecServers;
    @Value("${kepler.sql.rule.kafka.groupId:sqlRuleExecConsumerGroup}")
    private String sqlRuleExecGroupId;
    @Value("${spring.kafka.consumer.enable-auto-commit:true}")
    private String enableAutoCommit;
    @Value("${spring.kafka.consumer.auto-commit-interval:1000}")
    private String autoCommitInterval;
    @Value("${spring.kafka.consumer.session.timeout.ms:1500}")
    private String sessionTimeoutMs;

    @Value("${ad.exposure.result.kafka.consumerServers:}")
    private String adExposureResultServers;
    @Value("${ad.exposure.result.kafka.groupId:adExposureResultConsumerGroup}")
    private String adExposureResultGroupId;

    @Value("${tgi.algo.kafka.consumerServers:}")
    private String tgiAlgoKafkaServers;
    @Value("${tgi.algo.kafka.groupId:tgiAlgo}")
    private String tgiAlgoKafkaGroupId;

    @Value("${compare.kafka.consumerServers:}")
    private String compareKafkaServers;

    @Value("${compare.kafka.groupId:compareUserProfileGroup}")
    private String compareKafkaGroupId;

    @Bean("adExposureResultContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, String> adExposureResultContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(adExposureConsumerFactory());
        factory.getContainerProperties().setPollTimeout(1500);
        return factory;
    }

    @Bean
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> kafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory());
        factory.getContainerProperties().setPollTimeout(1500);
        return factory;
    }

    @Bean("tgiAlgoContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, String> tgiAlgoContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(tgiAlgoConsumerFactory());
        factory.getContainerProperties().setPollTimeout(1500);
        return factory;
    }

    @Bean("cmpContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, String> cmpContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(cmpConsumerFactory());
        factory.getContainerProperties().setPollTimeout(1500);
        return factory;
    }

    public ConsumerFactory<String, String> consumerFactory() {
        return new DefaultKafkaConsumerFactory<>(sqlRuleConsumerConfigs());
    }

    public ConsumerFactory<String, String> adExposureConsumerFactory() {
        return new DefaultKafkaConsumerFactory<>(adExposureConsumerConfigs());
    }

    public ConsumerFactory<String, String> tgiAlgoConsumerFactory() {
        return new DefaultKafkaConsumerFactory<>(tgiAlgoConsumerConfigs());
    }

    public ConsumerFactory<String, String> cmpConsumerFactory() {
        return new DefaultKafkaConsumerFactory<>(cmpConsumerConfigs());
    }

    public Map<String, Object> sqlRuleConsumerConfigs() {
        Map<String, Object> propsMap = new HashMap<>();
        propsMap.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, sqlRuleExecServers);
        propsMap.put(ConsumerConfig.GROUP_ID_CONFIG, sqlRuleExecGroupId);
        propsMap.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, enableAutoCommit);
        propsMap.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, autoCommitInterval);
        propsMap.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        propsMap.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        return propsMap;
    }

    public Map<String, Object> adExposureConsumerConfigs() {
        Map<String, Object> propsMap = new HashMap<>();
        propsMap.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, adExposureResultServers);
        propsMap.put(ConsumerConfig.GROUP_ID_CONFIG, adExposureResultGroupId);
        propsMap.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, enableAutoCommit);
        propsMap.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, autoCommitInterval);
        propsMap.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        propsMap.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        return propsMap;
    }

    public Map<String, Object> tgiAlgoConsumerConfigs() {
        Map<String, Object> propsMap = new HashMap<>();
        propsMap.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, tgiAlgoKafkaServers);
        propsMap.put(ConsumerConfig.GROUP_ID_CONFIG, tgiAlgoKafkaGroupId);
        propsMap.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, enableAutoCommit);
        propsMap.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, autoCommitInterval);
        propsMap.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        propsMap.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, ByteArrayDeserializer.class);
        return propsMap;
    }

    public Map<String, Object> cmpConsumerConfigs() {
        Map<String, Object> propsMap = new HashMap<>();
        propsMap.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, compareKafkaServers);
        propsMap.put(ConsumerConfig.GROUP_ID_CONFIG, compareKafkaGroupId);
        propsMap.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, enableAutoCommit);
        propsMap.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, autoCommitInterval);
        propsMap.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        propsMap.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        return propsMap;
    }
    @Bean
    public KafkaConsumer listener() {
        return new KafkaConsumer();
    }

}
