package com.ddmc.tag.dao.tag.rule;

import com.ddmc.tag.dao.base.BaseMysqlDao;
import com.ddmc.tag.model.rule.AlgoNeeds;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface AlgoNeedsMapper extends BaseMysqlDao<AlgoNeeds, Long> {

    List<AlgoNeeds> selectAllValid(@Param("id") Long id,
                                   @Param("productRuleId") Long productRuleId,
                                   @Param("userRuleId") Long userRuleId,
                                   @Param("algoModelId") Long algoModelId,
                                   @Param("needsStatus") Integer needsStatus,
                                   @Param("creator") String creator,
                                   @Param("startIndex") Long startIndex,
                                   @Param("pageSize") Long pageSize);

    Long countAllValid(@Param("id") Long id,
                       @Param("productRuleId") Long productRuleId,
                       @Param("userRuleId") Long userRuleId,
                       @Param("algoModelId") Long algoModelId,
                       @Param("needsStatus") Integer needsStatus,
                       @Param("creator") String creator);

    Long updateStatus(@Param("id") Long id, @Param("status") Integer status);

    Long invalid(@Param("id") Long id);

    AlgoNeeds selectValidNeedById(@Param("id") Long id);

    AlgoNeeds selectValidNeedByProductRuleId(@Param("productRuleId") Long productRuleId);

    List<AlgoNeeds> selectByNeedStatuses(@Param("needStatuses") List<Integer> needStatuses);
    List<AlgoNeeds> selectByNeedStatusesAndUpdateTime(@Param("needStatuses") List<Integer> needStatuses, @Param(
        "updateTime") Date updateTime);
}

