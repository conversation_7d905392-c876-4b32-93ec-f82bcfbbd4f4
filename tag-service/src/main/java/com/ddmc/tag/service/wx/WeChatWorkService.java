package com.ddmc.tag.service.wx;

import cn.hutool.core.collection.CollectionUtil;
import com.ddmc.tag.config.RefreshScopeConfig;
import com.ddmc.tag.constant.RobotAlterMsgEnum;
import com.ddmc.tag.pojo.v1.request.wx.WeChatWorkRobotSendRequest;
import com.ddmc.tag.pojo.v1.response.wx.WeChatWorkRobotResponse;
import com.ddmc.tag.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/25 16:30
 */
@Service
@Slf4j
public class WeChatWorkService {

    @Autowired
    private WeChatWorkClient weChatWorkClient;
    @Autowired
    private RefreshScopeConfig.FieldMappingHolder fieldMappingHolder;

    @Value("${tag.wechatwork.robot.inner.key:041c0c83-f64c-4137-bade-3e32797a3622}")
    private String key;
    @Value("${tag.wechatwork.robot.out.key:041c0c83-f64c-4137-bade-3e32797a3622}")
    private String secondKey;

    @Value("${tag.wechatwork.robot.business.key:11365815-f002-4825-b191-fd873b1deabb}")
    private String businessKey;

    @Value("${tag.wechatwork.robot.out.product.business.key:041c0c83-f64c-4137-bade-3e32797a3622}")
    private String productBusinessKey;

    @Value("${tag.wechatwork.robot.switch:true}")
    private boolean robotSwitch;

    public void sendCheckJobRobotTextContent(List<String> errorRuleIds){
        if(!robotSwitch){
            LogUtil.info(log, "机器人告警开关未开启,无需规则校验告警.");
            return;
        }
        boolean isSuccess = CollectionUtil.isEmpty(errorRuleIds);
        String msg = RobotAlterMsgEnum.getCheckRuleMatchMsg(isSuccess, errorRuleIds);
        WeChatWorkRobotResponse weChatWorkRobotResponse = weChatWorkClient.sendRobotTextContent(key, WeChatWorkRobotSendRequest.buildTextMsg(msg, fieldMappingHolder.getAlterMobiles()));
        LogUtil.info(log, "机器人告警响应:{}", weChatWorkRobotResponse);
    }
    public void sendCompareEsJobRobotTextContent(List<String> errorRuleIds){
        if(!robotSwitch){
            LogUtil.info(log, "机器人告警开关未开启,无需规则校验告警.");
            return;
        }
        boolean isSuccess = CollectionUtil.isEmpty(errorRuleIds);
        String msg = RobotAlterMsgEnum.getCompareEsMatchMsg(isSuccess, errorRuleIds);
        WeChatWorkRobotResponse weChatWorkRobotResponse = weChatWorkClient.sendRobotTextContent(key, WeChatWorkRobotSendRequest.buildTextMsg(msg, fieldMappingHolder.getAlterMobiles()));
        LogUtil.info(log, "机器人告警响应:{}", weChatWorkRobotResponse);
    }

    public void sendCheckJobRobotTextContent(String content){
        if(!robotSwitch){
            LogUtil.info(log, "机器人告警开关未开启,无需规则校验告警.");
            return;
        }
        WeChatWorkRobotResponse weChatWorkRobotResponse = weChatWorkClient.sendRobotTextContent(key, WeChatWorkRobotSendRequest.buildTextMsg(content, fieldMappingHolder.getAlterMobiles()));
        LogUtil.info(log, "机器人告警响应:{}", weChatWorkRobotResponse);
    }

    public void sendBusinessRobotTextContent(String content){
        if(!robotSwitch){
            LogUtil.info(log, "机器人告警开关未开启,无需规则校验告警.");
            return;
        }
        WeChatWorkRobotResponse weChatWorkRobotResponse = weChatWorkClient.sendRobotTextContent(businessKey, WeChatWorkRobotSendRequest.buildTextMsg(content, Collections.emptyList()));
        LogUtil.info(log, "机器人告警响应:{}", weChatWorkRobotResponse);
    }

    public void sendProductBusinessRobotTextContent(String content){
        if(!robotSwitch){
            LogUtil.info(log, "机器人告警开关未开启,无需规则校验告警.");
            return;
        }
        WeChatWorkRobotResponse weChatWorkRobotResponse = weChatWorkClient.sendRobotTextContent(productBusinessKey, WeChatWorkRobotSendRequest.buildTextMsg(content, Collections.emptyList()));
        LogUtil.info(log, "机器人告警响应:{}", weChatWorkRobotResponse);
    }

    public void sendProductBusinessRobotFormatTextContent(String format, Object... args){
        if(!robotSwitch){
            LogUtil.info(log, "机器人告警开关未开启,无需规则校验告警.");
            return;
        }
        String content = String.format(format, args);
        WeChatWorkRobotResponse weChatWorkRobotResponse = weChatWorkClient.sendRobotTextContent(productBusinessKey, WeChatWorkRobotSendRequest.buildTextMsg(content, Collections.emptyList()));
        LogUtil.info(log, "机器人告警响应:{}", weChatWorkRobotResponse);
    }

    public void sendZeroRuleRobotTextContent(List<String> zeroRuleIds){
        if(!robotSwitch){
            LogUtil.info(log, "机器人告警开关未开启,无需规则校验告警.");
            return;
        }
        boolean isSuccess = CollectionUtil.isEmpty(zeroRuleIds);
        String msg = RobotAlterMsgEnum.getZeroResultRuleMsg(isSuccess, zeroRuleIds);
        WeChatWorkRobotResponse weChatWorkRobotResponse = weChatWorkClient.sendRobotTextContent(secondKey, WeChatWorkRobotSendRequest.buildTextMsg(msg, fieldMappingHolder.getZeroRuleAlterMobiles()));
        LogUtil.info(log, "机器人规则匹配为0,告警响应:{}", weChatWorkRobotResponse);
    }


    public void sendTextContent(String key, String content){
        if(!robotSwitch){
            LogUtil.info(log, "机器人告警开关未开启,无需规则校验告警.");
            return;
        }

        WeChatWorkRobotResponse weChatWorkRobotResponse = weChatWorkClient.sendRobotTextContent(key, WeChatWorkRobotSendRequest.buildTextMsg(content, fieldMappingHolder.getAlterMobiles()));
        LogUtil.info(log, "机器人告警响应:{}", weChatWorkRobotResponse);
    }

    public void sendBusinessRobotMDContent(String content){
        if(!robotSwitch){
            LogUtil.info(log, "机器人告警开关未开启,无需规则校验告警.");
            return;
        }
        WeChatWorkRobotResponse weChatWorkRobotResponse = weChatWorkClient.sendRobotTextContent(businessKey, WeChatWorkRobotSendRequest.buildMDMsg(content, Collections.emptyList()));
        LogUtil.info(log, "机器人告警响应:{}", weChatWorkRobotResponse);
    }

    public void sendRobotMarkDownContentByKey(String content, String key){
        if(!robotSwitch){
            LogUtil.info(log, "机器人告警开关未开启,无需规则校验告警.");
            return;
        }

        WeChatWorkRobotResponse weChatWorkRobotResponse = weChatWorkClient.sendRobotTextContent(key, WeChatWorkRobotSendRequest.buildMDMsg(content, Collections.emptyList()));
        LogUtil.info(log, "机器人告警响应:{}", weChatWorkRobotResponse);
    }

}