package com.ddmc.tag.message.kafka;

import com.alibaba.fastjson2.JSON;
import com.csoss.monitor.api.cat.Cat;
import com.ddmc.jadot.client.dsl.mq.EntityBlock;
import com.ddmc.jadot.client.dsl.mq.MsgEndecoder;
import com.ddmc.jadot.client.dsl.mq.ProfileAppDataStateMsg;
import com.ddmc.jadot.client.dsl.mq.ProfileEntityBlockMsg;
import com.ddmc.tag.common.RuleExecCommonService;
import com.ddmc.tag.config.environment.DdmcEnvEnums;
import com.ddmc.tag.config.environment.EnvInvalidCondition;
import com.ddmc.tag.constant.TagConstants;
import com.ddmc.tag.converter.tgi.TgiAlgoConverter;
import com.ddmc.tag.enums.rule.ExecSource;
import com.ddmc.tag.enums.rule.TgiAlgoDataReadyStatsEnum;
import com.ddmc.tag.message.kafka.msg.OfflineRuleCircleMsg;
import com.ddmc.tag.message.kafka.msg.SqlRuleStatsExecMsg;
import com.ddmc.tag.message.kafka.msg.SqlRuleStatsMsg;
import com.ddmc.tag.message.kafka.msg.TgiAlgoDataReadyMsg;
import com.ddmc.tag.service.rule.AlgoNeedsService;
import com.ddmc.tag.service.rule.TgiAlgoUtils;
import com.ddmc.tag.util.MetricsUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Conditional;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

@Slf4j
@EnvInvalidCondition.InvalidByEnv({DdmcEnvEnums.SHT4PE})
@Conditional(EnvInvalidCondition.class)
@Component
public class KafkaConsumer {

    public static final String OFFLINE_CIRCLE_TOPICS = "${tgi.algo.topics}";
    public static final String TGI_ALGO_DATA_READY_TOPICS = "${tgi.algo.data.ready.topics}";
    public static final String OFFLINE_CIRCLE_GROUP = "tgi_algo_exec_group";
    public static final String TGI_ALGO_DATA_READY_GROUP = "tgi_algo_data_ready_group";

    @Resource
    private TagConstants tagConstants;

    @Resource
    @Qualifier("ingesterRuleExecCommonService")
    private RuleExecCommonService ingesterRuleExecCommonService;

    @Resource
    @Qualifier("tgiAlgoRuleExecCommonService")
    private RuleExecCommonService tgiAlgoRuleExecCommonService;

    @Resource
    private AlgoNeedsService algoNeedsService;

    @Resource
    private ObjectMapper om;

    @KafkaListener(topics = "ingester.report.crowd.label")
    public void sqlRuleStatsConsume(ConsumerRecord<?, ?> record) {
        if (!tagConstants.isKeplerSqlRuleExecSwitch()) {
            Cat.logEvent("sql_rule_exec_fail", "switch_off");
            return;
        }
        String data = "";
        try {
            data = (String) record.value();
            SqlRuleStatsMsg sqlRuleStats = JSON.parseObject(data, SqlRuleStatsMsg.class);
            SqlRuleStatsExecMsg sqlRuleStatsExecMsg = getSqlRuleStatsExecMsg(sqlRuleStats);

            ingesterRuleExecCommonService.exec(sqlRuleStatsExecMsg);
        } catch (Exception e) {
            log.error("KafkaConsumer.sqlRuleStatsConsume.msg.transfer.exception;msg={}; ", data, e);
            Cat.logEventWithSpan("sql_rule_exec_fail", "exception", "1", data);
        }
    }

    /**
     * tgi算法圈选消费
     * <p>
     *
     * @param
     */
    @KafkaListener(topics = OFFLINE_CIRCLE_TOPICS, groupId = OFFLINE_CIRCLE_GROUP, containerFactory = "tgiAlgoContainerFactory")
    public void tgiAlgoExecConsumer(ConsumerRecord<?, ?> record) {
        if (record == null || record.value() == null) {
            log.error("kafka-listener receive a null record");
            return;
        }

        try {
            ProfileEntityBlockMsg msg = SerializationUtils.deserialize((byte[]) record.value());
            OfflineRuleCircleMsg offlineRuleCircleMsg = convert(msg);
            log.info("tag-algo-circle, execId:{}, cnt:{}, currBlockId={}", offlineRuleCircleMsg.getExecutionId(),
                offlineRuleCircleMsg.getObjects().size(), offlineRuleCircleMsg.getCurrentBlockId());

            MetricsUtils.logEventWithSpan("sql_rule_exec_forward", "executing",
                    "execId:" + offlineRuleCircleMsg.getExecutionId());

            //log.info("kafka-listener receive a record:{}, timestamps", record.value());

            processPayload(record.timestamp(), offlineRuleCircleMsg);
        } catch (Exception e) {
            log.error("kafka-listener receive a record:{}, timestamps", record.value(), e);
        }

    }

////    @KafkaListener(topics = CompareUserProfileKafkaConfiguration.COMPARE_USER_PROFILE_TOPIC,
////        groupId = "compare_user_profile_group", containerFactory = "cmpContainerFactory")
//    public void cmpConsumer(ConsumerRecord<String, String> record) {
//        if (record == null || record.value() == null) {
//            log.error("kafka-listener receive a null record");
//            return;
//        }
//
//        try {
//            log.info("cmpConsumer, uids:{}", record.value());
//            //use jackson to parse from a json string to create a json node to get user_id as a string list
//            JsonNode treeNode = this.om.readTree(record.value());
//            JsonNode userIdNodes = treeNode.get("user_id");
//            List<String> uids = Lists.newArrayList();
//            for (JsonNode userIdNode: userIdNodes) {
//                uids.add(userIdNode.asText());
//            }
////            List<String> uids = om.readValue(record.value(), new TypeReference<List<String>>() {});
////            UserProfile userProfile = om.readValue(record.value(), UserProfile.class);
//            this.userProfileEsComparativeService.compare(uids);
//        } catch (Exception e) {
//            log.error("kafka-listener receive a record:{}, timestamps", record.value(), e);
//        }
//
//    }

    private OfflineRuleCircleMsg convert(ProfileEntityBlockMsg msg) {
        OfflineRuleCircleMsg offlineRuleCircleMsg = new OfflineRuleCircleMsg();
        offlineRuleCircleMsg.setMsgId(msg.getMsgId());
        EntityBlock payload = msg.getPayload();
        offlineRuleCircleMsg.setExecutionId(payload.getExecutionId());
        offlineRuleCircleMsg.setStrategyId(payload.getStrategyId());
        offlineRuleCircleMsg.setCurrentBlockId(payload.getCurrentBlockId());
        offlineRuleCircleMsg.setBlockIds(payload.getBlockIds());
        offlineRuleCircleMsg.setTotalObjects(payload.getTotalObjects());
        offlineRuleCircleMsg.setIsCompleted(payload.isIsCompleted());
        offlineRuleCircleMsg.setObjectType(payload.getObjectType());
        byte[] objectsBytes = payload.getObjects();
        String decompressObjects = MsgEndecoder.decompress(objectsBytes);

        try {
            offlineRuleCircleMsg.setObjects(this.om.readValue(decompressObjects, new TypeReference<List<String>>() {}));
        } catch (IOException e) {
            log.error("kafka-listener receive a record with objects error:{}, timestamps", decompressObjects, e);
        }
        return offlineRuleCircleMsg;

    }

    public void processPayload(Long timestamp, OfflineRuleCircleMsg payload) {
        SqlRuleStatsExecMsg sqlRuleStatsExecMsg = getSqlRuleStatsExecMsg(payload, timestamp);

        tgiAlgoRuleExecCommonService.exec(sqlRuleStatsExecMsg);
    }

    /**
     * tgiAlgoDataReady consumer
     *
     */
    @KafkaListener(topics = TGI_ALGO_DATA_READY_TOPICS, groupId = TGI_ALGO_DATA_READY_GROUP,
        containerFactory = "tgiAlgoContainerFactory")
    public void tgiAlgoDataReadyConsumer(ConsumerRecord<?, ?> record) {
        TgiAlgoDataReadyMsg readyMsg = null;

        try {
            ProfileAppDataStateMsg profileAppDataStateMsg = SerializationUtils.deserialize((byte[]) record.value());
            processDataReadyMsg(TgiAlgoConverter.convert(profileAppDataStateMsg));

        } catch (Exception e) {
            log.error("KafkaConsumer.tgiAlgoDataReadyConsumer.msg.transfer.exception;msg={}; ", record.key(), e);
            Cat.logEventWithSpan("tgi_algo_data_ready_fail", "exception", "1", "");
        }
    }

    public void processDataReadyMsg(TgiAlgoDataReadyMsg readyMsg) {
        String state = readyMsg.getState();

        if (!TgiAlgoUtils.isReady(readyMsg)) {
            Cat.logEventWithSpan("tgi_algo_data_ready_fail", "pvalue_not_match", "1", "");
            return;
        }

        if (StringUtils.equals(TgiAlgoDataReadyStatsEnum.READY.getCode(), state)) {
            algoNeedsService.updateAlgoNeedsDataReady(readyMsg);
        }
    }


    private static SqlRuleStatsExecMsg getSqlRuleStatsExecMsg(SqlRuleStatsMsg sqlRuleStats) {
        SqlRuleStatsExecMsg sqlRuleStatsExecMsg = new SqlRuleStatsExecMsg();
        sqlRuleStatsExecMsg.setType(sqlRuleStats.getType());
        sqlRuleStatsExecMsg.setSyncId(String.valueOf(sqlRuleStats.getSyncId()));
        sqlRuleStatsExecMsg.setReportId(String.valueOf(sqlRuleStats.getReportId()));
        sqlRuleStatsExecMsg.setReportName(sqlRuleStats.getReportName());
        sqlRuleStatsExecMsg.setTimestamp(sqlRuleStats.getTimestamp());
        sqlRuleStatsExecMsg.setObjects(sqlRuleStats.getUids());
        sqlRuleStatsExecMsg.setTotalSize(sqlRuleStats.getTotalSize());
        sqlRuleStatsExecMsg.setSyncBy(sqlRuleStats.getSyncBy());
        sqlRuleStatsExecMsg.setSyncEmail(sqlRuleStats.getSyncEmail());
        sqlRuleStatsExecMsg.setExecSource(null);
        sqlRuleStatsExecMsg.setMsgType("sql");
        return sqlRuleStatsExecMsg;
    }


    private static SqlRuleStatsExecMsg getSqlRuleStatsExecMsg(OfflineRuleCircleMsg msg, long timestamp) {
        SqlRuleStatsExecMsg sqlRuleStatsExecMsg = new SqlRuleStatsExecMsg();
        sqlRuleStatsExecMsg.setType(StringUtils.equals(msg.getObjectType(), "user") ? 1 : 2);
        sqlRuleStatsExecMsg.setSyncId(msg.getExecutionId());
        sqlRuleStatsExecMsg.setReportId(msg.getStrategyId());
        sqlRuleStatsExecMsg.setReportName(msg.getStrategyId());
        sqlRuleStatsExecMsg.setTimestamp(timestamp);
        sqlRuleStatsExecMsg.setObjects(msg.getObjects());
        sqlRuleStatsExecMsg.setTotalSize(msg.getTotalObjects().intValue());
        sqlRuleStatsExecMsg.setSyncBy("");
        sqlRuleStatsExecMsg.setSyncEmail("");
        sqlRuleStatsExecMsg.setExecSource(ExecSource.Kafka.getSource());
        sqlRuleStatsExecMsg.setMsgType("tgi");
        sqlRuleStatsExecMsg.setIsLastMsg(msg.getIsCompleted());
        return sqlRuleStatsExecMsg;
    }

}
