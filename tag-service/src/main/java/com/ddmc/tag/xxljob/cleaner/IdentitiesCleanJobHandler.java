package com.ddmc.tag.xxljob.cleaner;

import com.ddmc.tag.config.RedisCacheConfig4Api;
import com.ddmc.tag.redis.TagRedisUtil;
import com.ddmc.tag.sdk.constant.Constants;
import com.ddmc.tag.util.MetricsUtils;
import com.ddmc.tag.util.ThreadsUtils;
import com.ddmc.utils.json.JsonUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 好商品/品类新客身份集清理任务
 * 
 * Redis连接池优化建议：
 * 1. 增加maxActive连接数：JedisPoolConfig.setMaxTotal(int)，默认8，建议30+
 * 2. 增加最大空闲连接：JedisPoolConfig.setMaxIdle(int)，建议与maxActive接近
 * 3. 增加获取连接超时：JedisPoolConfig.setMaxWaitMillis(long)，建议5000ms(5秒)
 * 4. 检测连接有效性：JedisPoolConfig.setTestOnBorrow(true)，避免获取到无效连接
 * 5. 定期驱逐空闲连接：JedisPoolConfig.setTimeBetweenEvictionRunsMillis()，建议30000ms
 */
@Slf4j
@Component
public class IdentitiesCleanJobHandler {

    @Resource(name = RedisCacheConfig4Api.TAG_REDIS_CLIENT_UTIL_4API)
    private TagRedisUtil redisUtil;

    @Value("${redis.cleanup.enabled:true}")
    private boolean cleanupEnabled;

    @Value("${redis.cleanup.unlink.enabled:true}")
    private boolean cleanupUnlinkEnabled;

    // 任务停止标志
    private final AtomicBoolean stopRequested = new AtomicBoolean(false);

    @XxlJob(value = "IdentitiesCleanJob")
    public ReturnT<String> execute(String param) {
        try {
            // 重置停止标志
            stopRequested.set(false);
            
            // 记录任务开始
            XxlJobLogger.log("# IdentitiesCleanJob execute start, param : {} ", param);
            LocalDateTime taskStartTime = LocalDateTime.now();
            long startTime = System.currentTimeMillis();

            // 启动后台线程检查任务终止信号
            startTerminationDetector();

            // 1. 解析参数
            IdentitiesCleanJobParams params = parseParams(param);
            if (params == null || !validateParams(params)) {
                MetricsUtils.logEventWithSpan("iden_clean",
                        "stopped:invalid_params",
                        param);
                return ReturnT.FAIL;
            }

            // 2. 检查时间窗口
            if (!isInCleanupTimeWindow(params, taskStartTime)) {
                XxlJobLogger.log("# IdentitiesCleanJob stopped, reason=not_in_cleanup_window");
                MetricsUtils.logEventWithSpan("iden_clean",
                        "stopped:window_off",
                        "");
                return ReturnT.FAIL;
            }

            try {
                // 3. 执行清理
                AtomicInteger cleanedKeysCount = new AtomicInteger(0);
                boolean allCompleted = processPatterns(params, taskStartTime, cleanedKeysCount);

                // 4. 记录结果
                long duration = (System.currentTimeMillis() - startTime) / 1000;
                recordSummary(cleanedKeysCount.get(), duration, allCompleted);
                
                return ReturnT.SUCCESS;
            } finally {
                // 无论成功失败，都确保资源正确清理
                cleanupResources();
            }
        } catch (Exception e) {
            log.error("IdentitiesCleanJob.execute.error", e);
            MetricsUtils.logEventWithSpan("iden_clean",
                    String.format("error:main_process,error:%s", e.getMessage()),
                    "");
            return ReturnT.FAIL;
        }
    }

    // ============================== 参数处理 ==============================

    /**
     * 解析任务参数
     */
    private IdentitiesCleanJobParams parseParams(String param) {
        if (StringUtils.isBlank(param)) {
            XxlJobLogger.log("# IdentitiesCleanJob parseParams failed: param is blank");
            return null;
        }
        try {
            return JsonUtil.jsonToBean(param, IdentitiesCleanJobParams.class);
        } catch (Exception e) {
            log.error("IdentitiesCleanJob.parseParams.parse.error", e);
            XxlJobLogger.log("# IdentitiesCleanJob parseParams failed: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 验证参数有效性
     */
    private boolean validateParams(IdentitiesCleanJobParams params) {
        if (Objects.isNull(params)) {
            XxlJobLogger.log("# IdentitiesCleanJob stopped, reason=param_invalid");
            return false;
        }
        
        // 验证执行时长
        if (Objects.isNull(params.getJobMaxDurationSeconds()) || params.getJobMaxDurationSeconds() <= 0) {
            XxlJobLogger.log("# IdentitiesCleanJob stopped, reason=invalid_duration");
            return false;
        }
        
        // 验证模式列表
        List<String> patterns = params.getRedisKeyPatterns();
        if (CollectionUtils.isEmpty(patterns)) {
            XxlJobLogger.log("# IdentitiesCleanJob stopped, reason=patterns_empty");
            return false;
        }
        
        return true;
    }

    // ============================== 模式处理逻辑 ==============================

    /**
     * 处理所有模式
     */
    private boolean processPatterns(IdentitiesCleanJobParams params, LocalDateTime taskStartTime, 
                                   AtomicInteger cleanedKeysCount) {
        List<String> patterns = params.getRedisKeyPatterns();
        boolean parallelMode = Boolean.parseBoolean(params.getEnableParallelProcessing() != null ? 
                params.getEnableParallelProcessing().toString() : "false");
        
        // 记录所有处理结果
        boolean allCompleted;
        
        if (parallelMode) {
            // 并行处理模式
            XxlJobLogger.log("# IdentitiesCleanJob running in parallel mode for {} patterns", patterns.size());
            allCompleted = processInParallel(patterns, params, taskStartTime, cleanedKeysCount);
        } else {
            // 串行处理模式
            XxlJobLogger.log("# IdentitiesCleanJob running in serial mode for {} patterns", patterns.size());
            allCompleted = processInSerial(patterns, params, taskStartTime, cleanedKeysCount);
        }
        
        return allCompleted;
    }

    /**
     * 串行处理模式
     */
    private boolean processInSerial(List<String> patterns, IdentitiesCleanJobParams params, 
                                   LocalDateTime taskStartTime, AtomicInteger cleanedKeysCount) {
        for (String pattern : patterns) {
            // 检查终止信号
            if (isTaskTerminated()) {
                XxlJobLogger.log("# IdentitiesCleanJob terminated during serial processing");
                MetricsUtils.logEventWithSpan("iden_clean",
                        "stopped:terminated_during_serial,pattern:" + pattern,
                        "");
                return false;
            }
            
            // 处理单个模式
            XxlJobLogger.log("# IdentitiesCleanJob starting pattern: {}", pattern);
            boolean patternSuccess = clean(pattern, cleanedKeysCount, params, taskStartTime);
            
            if (!patternSuccess) {
                XxlJobLogger.log("# IdentitiesCleanJob pattern {} failed, continuing with next pattern", pattern);
                MetricsUtils.logEventWithSpan("iden_clean", "fail:pattern:" + pattern, "");
            }
            
            // 检查时间窗口
            if (!isInCleanupTimeWindow(params, taskStartTime)) {
                MetricsUtils.logEventWithSpan("iden_clean", "stopped:window_off,pattern:" + pattern, "");
                return false;
            }
            
            // 模式之间的间隔，给连接池恢复时间
            sleep(1000);
        }
        
        return !stopRequested.get();
    }

    /**
     * 并行处理模式
     */
    private boolean processInParallel(List<String> patterns, IdentitiesCleanJobParams params,
                                     LocalDateTime taskStartTime, AtomicInteger cleanedKeysCount) {
        try {
            // 收集所有清理任务
            List<CompletableFuture<Boolean>> allCleanTasks = new ArrayList<>();
            Map<String, CompletableFuture<Boolean>> patternTaskMap = new HashMap<>();
            
            for (String pattern : patterns) {
                CompletableFuture<Boolean> cleanTask = CompletableFuture.supplyAsync(() -> 
                    clean(pattern, cleanedKeysCount, params, taskStartTime), 
                    ThreadsUtils.getCleanThreadPool());
                
                allCleanTasks.add(cleanTask);
                patternTaskMap.put(pattern, cleanTask);
            }
            
            // 等待所有任务完成或被终止
            CompletableFuture<Void> allOf = CompletableFuture.allOf(
                allCleanTasks.toArray(new CompletableFuture[0]));
            
            // 添加监控终止的任务
            CompletableFuture<Boolean> terminationMonitor = monitorTermination(allOf);
            
            // 等待任务完成或被终止
            CompletableFuture.anyOf(allOf, terminationMonitor).get();
            
            // 检查结果并记录失败的模式
            boolean anyFailed = false;
            List<String> failedPatterns = new ArrayList<>();
            
            for (Map.Entry<String, CompletableFuture<Boolean>> entry : patternTaskMap.entrySet()) {
                String pattern = entry.getKey();
                CompletableFuture<Boolean> task = entry.getValue();
                
                if (task.isDone()) {
                    try {
                        if (!task.get()) {
                            anyFailed = true;
                            failedPatterns.add(pattern);
                            XxlJobLogger.log("# Pattern {} failed during parallel processing", pattern);
                        }
                    } catch (Exception e) {
                        anyFailed = true;
                        failedPatterns.add(pattern);
                        log.error("Error getting result for pattern: {}", pattern, e);
                    }
                }
            }
            
            if (anyFailed) {
                MetricsUtils.logEventWithSpan("iden_clean", 
                        "stopped:subtask_failed_during_parallel", 
                        "patterns:" + patterns.size() + ",failed:" + String.join(",", failedPatterns));
            }
            
            return !anyFailed;
        } catch (Exception e) {
            log.error("Parallel pattern processing error", e);
            MetricsUtils.logEventWithSpan("iden_clean",
                    String.format("error:parallel_exception,error:%s", e.getMessage()),
                    "patterns:" + patterns.size());
            return false;
        }
    }

    /**
     * 监控任务终止
     */
    private CompletableFuture<Boolean> monitorTermination(CompletableFuture<?> taskToMonitor) {
        return CompletableFuture.supplyAsync(() -> {
            while (!taskToMonitor.isDone() && !stopRequested.get()) {
                if (isTerminated()) {
                    stopRequested.set(true);
                    XxlJobLogger.log("# IdentitiesCleanJob termination detected while running tasks");
                    MetricsUtils.logEventWithSpan("iden_clean", "stopped:monitor_termination_detected", "");
                    return true;
                }
                sleep(1000);
            }
            return stopRequested.get();
        });
    }

    // ============================== 单个模式清理逻辑 ==============================

    /**
     * 清理单个模式的键
     */
    public boolean clean(String pattern, AtomicInteger cleanedCount,
                      IdentitiesCleanJobParams params, LocalDateTime taskStartTime) {
        try {
            long startTime = System.currentTimeMillis();

            // 获取结束时间
            LocalDateTime endTime = taskStartTime.plusSeconds(params.getJobMaxDurationSeconds());
            
            // 记录当前模式的清理数量
            AtomicInteger patternCleanedCount = new AtomicInteger(0);
            
            // 处理游标续传
            String cursorCacheKey = "clean:cursor:" + pattern;
            String lastCursor = params.getIgnoreLastPosition() ? null : redisUtil.getStringCache(cursorCacheKey);
            logCursorInfo(pattern, lastCursor, params.getIgnoreLastPosition());
            
            // 执行扫描并同时清理键（直接在扫描过程中处理）
            scanKeysForCleaning(pattern, params, endTime, cursorCacheKey, lastCursor, patternCleanedCount);
            
            // 检查是否被终止
            if (stopRequested.get()) {
                XxlJobLogger.log("# 模式 {} 的清理任务被终止", pattern);
                return false;
            }
            
            // 更新总清理计数
            int thisPatternCleaned = patternCleanedCount.get();
            cleanedCount.addAndGet(thisPatternCleaned);
            
            // 记录结果
            MetricsUtils.logEventWithSpan("iden_clean",
                    String.format("summary_sub:%s,cleaned:%d,time:%ds", pattern, thisPatternCleaned, (System.currentTimeMillis()-startTime)/1000), "");
            XxlJobLogger.log("# 模式 {} 清理完成，共清理: {} 键", pattern, thisPatternCleaned);

            
            return true;
        } catch (Exception e) {
            log.error("清理模式 {} 时出错", pattern, e);
            return false;
        }
    }

    /**
     * 扫描并收集需要清理的键
     */
    @SuppressWarnings("unchecked")
    private void scanKeysForCleaning(String pattern, IdentitiesCleanJobParams params,
                                     LocalDateTime endTime, String cursorCacheKey,
                                     String lastCursor, AtomicInteger patternCleanedCount) {
        final int batchSize = params.getBatchCleanSize() != null ? params.getBatchCleanSize() : 50;
        List<String> currentBatch = new ArrayList<>(batchSize); // 当前批次的键
        
        // 配置扫描选项
        ScanOptions scanOptions = ScanOptions.scanOptions()
            .match(pattern)
            .count(params.getRedisScanCount())
            .build();
        
        // 执行扫描
        redisUtil.getiCacheManager().getRedisTemplate().execute((RedisCallback<Long>) connection -> {
            try {
                long count = 0;
                
                // 开始扫描并定位上次位置
                Cursor<byte[]> cursor = connection.scan(scanOptions);
                if (StringUtils.isNotBlank(lastCursor)) {
                    cursor = findLastCursorPosition(cursor, lastCursor);
                }
                
                try {
                    // 处理扫描出的键
                    while (cursor.hasNext() && !stopRequested.get()) {
                        // 检查任务终止
                        if (count > 0 && count % 100 == 0 && isTerminated()) {
                            stopRequested.set(true);
                            break;
                        }
                        
                        // 获取键
                        String key = new String(cursor.next());
                        
                        // 检查时间窗口
                        if (LocalDateTime.now().isAfter(endTime)) {
                            // 保存断点并退出
                            redisUtil.setValue(cursorCacheKey, key, Constants.ONE_DAY_SECONDS);
                            XxlJobLogger.log("# 时间窗口关闭，保存光标位置: {}", key);
                            break;
                        }
                        
                        // 收集键到当前批次
                        currentBatch.add(key);
                        count++;
                        MetricsUtils.logEventWithSpan("iden_clean", "scanned_total","");

                        // 当达到批处理大小时，立即执行清理
                        if (currentBatch.size() >= batchSize) {
                            doBatchClean(currentBatch, pattern, patternCleanedCount, params);
                            MetricsUtils.logEventWithSpan("iden_clean", "cleaned_batch_" + batchSize,"");

                            // 清空当前批次
                            currentBatch.clear();

                            // 控制清理速率
                            applyRateLimiting(params, "clean");
                        }
                        
                        // 控制扫描速率
                        applyRateLimiting(params, "scan");
                    }
                    
                    // 处理最后一批不足batchSize的键
                    if (!currentBatch.isEmpty() && !stopRequested.get()) {
                        MetricsUtils.logEventWithSpan("iden_clean", "cleaned_last_batch_" + currentBatch.size(),"");
                        doBatchClean(currentBatch, pattern, patternCleanedCount, params);
                    }
                    
                    return count;
                } finally {
                    // 确保cursor关闭
                    try {
                        cursor.close();
                    } catch (Exception e) {
                        log.warn("关闭Redis游标时出错: {}", e.getMessage());
                    }
                }
            } catch (Exception e) {
                log.error("扫描模式 {} 出错", pattern, e);
                // 保存当前进度，允许下次从这里继续
                if (!currentBatch.isEmpty()) {
                    try {
                        redisUtil.setValue(cursorCacheKey, currentBatch.get(0), Constants.ONE_DAY_SECONDS);
                    } catch (Exception saveEx) {
                        log.error("保存断点失败", saveEx);
                    }
                }
                throw e;
            }
        });
    }

    /**
     * 查找上次处理位置
     */
    private Cursor<byte[]> findLastCursorPosition(Cursor<byte[]> cursor, String lastCursor) {
        boolean foundLastPosition = false;
        int maxScanCount = 10000;
        int scanCount = 0;
        
        XxlJobLogger.log("# IdentitiesCleanJob finding last cursor position: {}", lastCursor);
        
        // 处理Redis中的引号问题
        String normalizedLastCursor = lastCursor;
        if (normalizedLastCursor.startsWith("\"") && normalizedLastCursor.endsWith("\"")) {
            normalizedLastCursor = normalizedLastCursor.substring(1, normalizedLastCursor.length() - 1);
        }
        
        // 扫描直到找到上次位置
        while (cursor.hasNext() && !foundLastPosition && !stopRequested.get() && scanCount < maxScanCount) {
            String currentKey = new String(cursor.next());
            scanCount++;
            
            if (currentKey.equals(normalizedLastCursor)) {
                foundLastPosition = true;
                XxlJobLogger.log("# Found last cursor position after {} scans", scanCount);
            }
        }
        
        // 如果超出最大扫描次数
        if (!foundLastPosition && scanCount >= maxScanCount) {
            XxlJobLogger.log("# Reached max scan count while looking for cursor");
        }
        
        return cursor;
    }

    /**
     * 执行单批次清理
     */
    @SuppressWarnings("unchedked")
    private void doBatchClean(List<String> keys, String pattern, AtomicInteger expiredCount, IdentitiesCleanJobParams params) {
        if (keys == null || keys.isEmpty() || stopRequested.get() || !cleanupUnlinkEnabled) {
            return;
        }
        
        try {
            // 使用参数配置的过期时间范围，如果未设置则使用默认值
            long minExpirationSeconds = params.getKeyMinExpirySeconds() != null ? 
                    params.getKeyMinExpirySeconds() : 3600;   // 默认1小时
            long maxExpirationSeconds = params.getKeyMaxExpirySeconds() != null ? 
                    params.getKeyMaxExpirySeconds() : 7200;   // 默认2小时
                
            int miniBatchSize = params.getMiniOpBatchSize() != null ? params.getMiniOpBatchSize() : 10;
            
            // 设置过期时间
            int successCount = 0;
            for (int i = 0; i < keys.size() && !stopRequested.get(); i += miniBatchSize) {
                int endIdx = Math.min(i + miniBatchSize, keys.size());
                List<String> miniBatch = keys.subList(i, endIdx);
                
                for (String key : miniBatch) {
                    // 超宽范围随机过期时间，避免同时过期引起CPU峰值
                    long expirationSeconds = minExpirationSeconds;
                    
                    // 只有当最大值大于最小值时才使用随机范围
                    if (maxExpirationSeconds > minExpirationSeconds) {
                        expirationSeconds += ThreadLocalRandom.current().nextLong(0, 
                                maxExpirationSeconds - minExpirationSeconds + 1);
                    }
                    
                    // 直接使用RedisTemplate的expire方法设置过期时间
                    Boolean result = redisUtil.getiCacheManager().getRedisTemplate().expire(
                            key, expirationSeconds, TimeUnit.SECONDS);
                    MetricsUtils.logEventWithSpan("iden_clean", "cleaned_total","");
                    
                    if (result != null && result) {
                        successCount++;
                    }
                }
                
                sleep(20); // 短暂暂停释放连接
            }
            
            // 更新计数
            if (successCount > 0) {
                expiredCount.addAndGet(successCount);
            }
        } catch (Exception e) {
            log.error("Batch clean error, pattern:{}", pattern, e);
        } finally {
            // 强制清理连接池缓存资源
            forceCloseRedisConnection();
        }
    }

    private void forceCloseRedisConnection() {
        try {
            RedisCallback<Void> callback = connection -> {
                connection.close();
                return null;
            };
            redisUtil.getiCacheManager().getRedisTemplate().execute(callback);
        } catch (Exception e) {
            log.warn("Error during final Redis connection cleanup: {}", e.getMessage(), e);
        }
    }

    // ============================== 辅助方法 ==============================

    /**
     * 启动后台终止检测线程
     */
    private void startTerminationDetector() {
        Thread detector = new Thread(() -> {
            try {
                while (!Thread.currentThread().isInterrupted() && !stopRequested.get()) {
                    if (isTerminated()) {
                        stopRequested.set(true);
                        XxlJobLogger.log("# Background termination check detected stop request");
                        MetricsUtils.logEventWithSpan("iden_clean", "stopped:set_stop_detected_termination", "");
                        break;
                    }
                    Thread.sleep(1000);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                MetricsUtils.logEventWithSpan("iden_clean", "error:set_stop_detected_termination", "");
            }
        });
        detector.setDaemon(true);
        detector.start();
    }

    /**
     * 检测任务是否被终止
     */
    private boolean isTaskTerminated() {
        boolean terminated = stopRequested.get() || isTerminated();
        if (terminated) {
            MetricsUtils.logEventWithSpan("iden_clean",
                    String.format("is_terminated:%s", stopRequested.get() ? "stop_requested" : "xxl_terminated"),
                    "");
        }
        return terminated;
    }

    /**
     * 检测XXL-JOB是否请求终止任务
     */
    private boolean isTerminated() {
        try {
            // 检查线程中断状态
            boolean interrupted = Thread.currentThread().isInterrupted();
            
            // 检查XxlJob停止信号
            boolean toStop = false;
            try {
                StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
                for (StackTraceElement element : stackTrace) {
                    if (element.getClassName().contains("xxl.job") && 
                        element.getMethodName().contains("stop")) {
                        toStop = true;
                        break;
                    }
                }
                
                if (interrupted || toStop) {
                    XxlJobLogger.log("# Termination detected: interrupted={}, toStop={}", interrupted, toStop);
                    
                    // 添加终止埋点
                    String terminateReason = interrupted ? "thread_interrupted" : "xxl_stop_detected";
                    MetricsUtils.logEventWithSpan("iden_clean",
                            String.format("is_terminated:%s", terminateReason),
                            "");
                }
            } catch (Exception ignored) {
                MetricsUtils.logEventWithSpan("iden_clean", "error:is_terminated", "");
                return true;
            }
            
            return interrupted || toStop;
        } catch (Exception e) {
            log.warn("Error checking termination status", e);
            MetricsUtils.logEventWithSpan("iden_clean",
                    String.format("error:is_terminated,error:%s", e.getMessage()),
                    "");
            return true;
        }
    }

    /**
     * 休眠指定时间
     */
    private void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 应用速率限制
     */
    private void applyRateLimiting(IdentitiesCleanJobParams params, String operationType) {
        long delayMs;
        
        if ("scan".equals(operationType)) {
            delayMs = params.getScanIntervalMs() != null ? params.getScanIntervalMs() : 50;
        } else {
            delayMs = params.getCleanIntervalMs() != null ? params.getCleanIntervalMs() : 100;
        }
        
        if (delayMs > 0) {
            sleep(delayMs);
        }
    }

    /**
     * 记录游标信息
     */
    private void logCursorInfo(String pattern, String lastCursor, Boolean ignoreLastPosition) {
        if (Boolean.TRUE.equals(ignoreLastPosition)) {
            XxlJobLogger.log("# Forced start from beginning for pattern: {}", pattern);
        } else if (StringUtils.isNotBlank(lastCursor)) {
            XxlJobLogger.log("# Continuing from last cursor for pattern: {}, cursor: {}", pattern, lastCursor);
        } else {
            XxlJobLogger.log("# Starting from beginning for pattern: {}", pattern);
        }
    }

    /**
     * 检查是否在清理时间窗口内
     */
    private boolean isInCleanupTimeWindow(IdentitiesCleanJobParams params, LocalDateTime taskStartTime) {
        if (stopRequested.get()) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime endTime = taskStartTime.plusSeconds(params.getJobMaxDurationSeconds());
        
        return now.isEqual(taskStartTime) || (now.isAfter(taskStartTime) && now.isBefore(endTime));
    }

    /**
     * 记录任务执行总结
     */
    private void recordSummary(int cleanedCount, long duration, boolean completed) {
        String status = completed ? "completed" : "interrupted";

        XxlJobLogger.log("# IdentitiesCleanJob execute end, completed={}, cleaned={}, duration={}s",
                completed, cleanedCount, duration);
        
        MetricsUtils.logEventWithSpan("iden_clean",
                String.format("summary:status:%s,cleaned:%d,time:%ds", status, cleanedCount, duration), "");
    }

    /**
     * 任务执行完成前进行清理
     */
    private void cleanupResources() {
        try {
            // 确保所有连接正确关闭
            redisUtil.getiCacheManager().getRedisTemplate().execute((RedisCallback<Object>) connection -> {
                try {
                    connection.close();
                } catch (Exception e) {
                    log.warn("Error explicitly closing Redis connection: {}", e.getMessage());
                }
                return null;
            });
            
            // 记录清理完成
            MetricsUtils.logEventWithSpan("iden_clean", "resource_release", "");
        } catch (Exception e) {
            log.warn("Error during Redis resource cleanup: {}", e.getMessage());
            MetricsUtils.logEventWithSpan("iden_clean", "error:release:" + e.getMessage(), "");
        }
    }

    // ============================== 参数类 ==============================

    @Data
    private static class IdentitiesCleanJobParams {
        // 键模式配置
        private List<String> redisKeyPatterns;              // 需要清理的Redis键模式列表
        
        // 执行控制配置
        private Long jobMaxDurationSeconds;                 // 任务最大执行时长（秒）
        private Boolean enableParallelProcessing;           // 是否启用并行处理多个模式
        private Boolean ignoreLastPosition;                 // 是否忽略上次位置（强制从头开始）
        
        // 批处理配置
        private Integer batchCleanSize;                     // 每批次清理的键数量
        private Integer miniOpBatchSize;                    // 每个小型操作批次大小（默认10）
        
        // 速率控制配置
        private Integer redisScanCount;                     // Redis SCAN命令一次返回的键数量
        private Integer scanIntervalMs;                     // 扫描操作间隔时间（毫秒）
        private Integer cleanIntervalMs;                    // 清理操作间隔时间（毫秒）
        
        // 过期时间配置
        private Long keyMinExpirySeconds;                   // 键最小过期时间（秒）
        private Long keyMaxExpirySeconds;                   // 键最大过期时间（秒）
        
        // 监控与日志配置
        private Boolean enableDetailedMetrics;              // 是否启用详细监控指标
        private Integer cursorSaveIntervalKeys;             // 每处理多少键保存一次游标位置
    }
}
