package com.ddmc.tag.xxljob.monitor;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.ddmc.promorecall.vo.UserTagPlanInfoVo;
import com.ddmc.tag.bo.response.RecallPlanResponseBO;
import com.ddmc.tag.config.RedisCacheConfig4Rule;
import com.ddmc.tag.constant.CacheKeyConstants;
import com.ddmc.tag.constant.TagConstants;
import com.ddmc.tag.dao.tag.rule.RuleSettingDao;
import com.ddmc.tag.dao.tag.stats.RuleUsersDao;
import com.ddmc.tag.enums.category.CategoryEnum;
import com.ddmc.tag.enums.rule.RuleSettingSelectTypeEnum;
import com.ddmc.tag.enums.rule.RuleStatusEnum;
import com.ddmc.tag.infrastructure.rpc.SolarClient;
import com.ddmc.tag.infrastructure.rpc.promo.PromoRecallFacade;
import com.ddmc.tag.model.rule.RuleSetting;
import com.ddmc.tag.model.stats.RuleUsers;
import com.ddmc.tag.redis.TagRedisUtil;
import com.ddmc.tag.service.wx.WeChatWorkService;
import com.ddmc.tag.util.MetricsUtils;
import com.ddmc.tag.util.TimeUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 用户离线圈选波动对比（今日-昨日）
 *
 *  【已停止-生产】 7062	用户离线圈选波动对比（今日-昨日）	BEAN：MonitorRuleResultFluctuationAlertJob		0 0/4 5,6 * * ?
 *  【运行中-灰度】 7562	用户离线圈选波动对比（今日-昨日）	BEAN：MonitorRuleResultFluctuationAlertJob	0 0/3 5,6 * * ?
 */
@Component
@Slf4j
public class MonitorUserRulesSelectionFluctuationJob {

    @Autowired
    private TagConstants tagConstants;

    @Autowired
    private RuleUsersDao ruleUsersDao;

    @Autowired
    private RuleSettingDao ruleSettingDao;

    @Autowired
    private WeChatWorkService weChatWorkService;

    @Autowired
    private SolarClient solarClient;

    @Value("${robot.msg.max.length:1500}")
    private int robotMsgMaxLength;

    @Autowired
    private PromoRecallFacade promoRecallFacade;

    @Autowired
    @Qualifier(RedisCacheConfig4Rule.TAG_REDIS_CLIENT_UTIL_4RULE)
    private TagRedisUtil ruleRedisUtil;

    @XxlJob("MonitorUserRulesSelectionFluctuationJob")
    public ReturnT<String> monitorUserRulesSelectionFluctuationJob(String param) {

        try {

            log.info("# monitor user rules selection fluctuation start, param : {}", param);

            // 一.校验参数及今日圈选及比对是否完成
            String dateStr = DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN);
            ReturnT<String> result = checkParam(param, dateStr);
            if (Objects.nonNull(result)) {
                return result;
            }

            // 二.业务数据准备
            // 查询策略
            List<RuleSetting> userRuleSettings = ruleSettingDao.findByCategoryId(CategoryEnum.USER.getValue());
            Map<Long, RuleSetting> userRuleSettingMap = userRuleSettings.stream().collect(Collectors.toMap(RuleSetting::getId, Function.identity(), (v1, v2) -> v1));

            // 查询包含特定容易波动标签的策略id
            List<Long> volatilityRuleIds = queryVolatilityRuleIds();

            // 过滤得到需要比对的策略id
            List<Long> ruleIds = filterRuleIds(userRuleSettings, volatilityRuleIds);

            // 查询当天圈选结果
            List<RuleUsers> todayRuleUsers = ruleUsersDao.queryValidLatestRuleUsersByRuleIdsAndDate(ruleIds, DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN));
            if (CollectionUtils.isEmpty(todayRuleUsers)) { // 今天无圈选结果，直接返回比对失败
                log.error("monitorRuleResultFluctuationAlertJob today is not ready.");
                return ReturnT.FAIL;
            }

            // 查询前一天圈选结果
            List<RuleUsers> yesterdayRuleUsers = ruleUsersDao.queryValidLatestRuleUsersByRuleIdsAndDate(ruleIds, DateUtil.format(DateUtil.offsetDay(new Date(), -1), DatePattern.PURE_DATE_PATTERN));
            Map<Long, RuleUsers> yesterdayRuleUserMap = yesterdayRuleUsers.stream().collect(Collectors.toMap(RuleUsers::getRuleId, Function.identity(), (v1, v2) -> v1));

            // 三.构建消息体
            // 初始化波动数数量
            int bigResultRuleCount = 0, fluctuationRuleCount = 0, volatilityRuleCount = 0;

            // 初始化三个告警消息体
            StringBuilder bigResultRuleErrorMsg = new StringBuilder(), fluctuationRuleErrorMsg = new StringBuilder(), fluctuationVolatilityRuleErrorMsg = new StringBuilder();
            // 构建消息体

            buildMsgs(userRuleSettingMap, volatilityRuleIds, todayRuleUsers, yesterdayRuleUserMap, bigResultRuleCount,
                    fluctuationRuleCount, volatilityRuleCount, bigResultRuleErrorMsg, fluctuationRuleErrorMsg, fluctuationVolatilityRuleErrorMsg);

            // 四.发送提醒消息到指定微信群
            sendAlarmMsgs(bigResultRuleErrorMsg, fluctuationRuleErrorMsg, fluctuationRuleCount, fluctuationVolatilityRuleErrorMsg, volatilityRuleCount);

            // 五.写波动任务执行完成标记
            ruleRedisUtil.incrOne(CacheKeyConstants.getToDayLoadMonthFluctuationMonitorJobKey(dateStr));

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("monitorRuleResultFluctuationAlertJob error.", e);
            return ReturnT.FAIL;
        }
    }

    private ReturnT<String> checkParam(String param, String dateStr) {
        if (StringUtils.isBlank(param)) {
            // 需要校验
            log.info("# monitor user rules selection fluctuation param empty, check exec status");
            // 比对完成标记
            if(Objects.nonNull(ruleRedisUtil.getValue(CacheKeyConstants.getToDayLoadMonthFluctuationMonitorJobKey(dateStr)))){
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "今日比对已经完成,请勿重复执行");
            }
            // 今日数据flag check
            if (Objects.isNull(ruleRedisUtil.getValue(CacheKeyConstants.getToDayLoadMonthJobTagKey(dateStr)))) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "今日数据还未准备好！！");
            }
            // 今日圈选执行是否完成check (通过当天执行中的用户圈选执行数量是否为0)
            if (ruleSettingDao.findAllDailyExecutingRulesCount(CategoryEnum.USER.getValue()) > 0) {
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "今日圈选还未完成");
            }
        } else {
            // 不校验 直接执行
            log.info("# monitor user rules selection fluctuation skip check, param : {}", param);
        }
        return null;
    }

    private List<Long> filterRuleIds(List<RuleSetting> userRuleSettings, List<Long> volatilityRuleIds) {
        // 过滤用户策略(后期调整为通过sql直接过滤) & 配置的比对分类组下策略
        List<Long> ruleIds = userRuleSettings.stream()
                .filter(userRuleSetting -> !tagConstants.getFluctuationFilterGroupIds().contains(userRuleSetting.getGroupId()))
                .filter(userRuleSetting -> Objects.equals(RuleStatusEnum.USING.getValue(), userRuleSetting.getRuleStatus()))
                .map(RuleSetting::getId).collect(Collectors.toList());

        // 判断比对排除这部分容易波动的策略
        if (tagConstants.isVolatilityCompareFluctuationSwitch() && CollectionUtils.isNotEmpty(volatilityRuleIds)) {
            log.info("# monitor user rules selection fluctuation exclude wx rules, volatilityRuleIds : {}", volatilityRuleIds);
            ruleIds.removeAll(volatilityRuleIds);
        }

        log.info("# monitor user rules selection fluctuation start to handle finally, ruleIds : {}", ruleIds);
        return ruleIds;
    }

    private List<Long> queryVolatilityRuleIds() {
        // 过滤出包含指定标签的策略
        List<String> volatilityTags = tagConstants.getVolatilityTags();
        List<Long> volatilityRuleIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(volatilityTags)) {
            log.info("# monitor user rules selection fluctuation filter special tags, volatilityTags : {}", volatilityTags);
            volatilityRuleIds = ruleSettingDao.findFieldsIncludedRules(volatilityTags);
        }
        return volatilityRuleIds;
    }

    private void buildMsgs(Map<Long, RuleSetting> userRuleSettingMap, List<Long> volatilityRuleIds, List<RuleUsers> todayRuleUsers, Map<Long, RuleUsers> yesterdayRuleUserMap, int bigResultRuleCount, int fluctuationRuleCount,
                           int volatilityRuleCount, StringBuilder bigResultRuleErrorMsg, StringBuilder fluctuationRuleErrorMsg, StringBuilder fluctuationVolatilityRuleErrorMsg) {
        // 消息头
        String bigResultRuleErrorMsgHead = "<font color=\"warning\">用户策略新增大圈选集（超过500W）：</font>",
        fluctuationRuleErrorMsgHead = "<font color=\"warning\">非企微用户策略圈选集波动超过50%：</font>",
        fluctuationVolatilityRuleErrorMsgHead = "<font color=\"warning\">企微用户策略圈选集波动超过50%：</font>";
        // 消息体
        StringBuilder bigResultRuleErrorMsgBody = new StringBuilder(), fluctuationRuleErrorMsgBody = new StringBuilder(), fluctuationVolatilityRuleErrorMsgBody = new StringBuilder();
        // 消息尾
        String fluctuationRuleTotal = "\n共计%d条",
        // 用于投放消息尾
        recallPlanTail = "<font color=\"warning\">(用于投放计划)</font>";

        for (RuleUsers todayRuleUser : todayRuleUsers) {
            RuleSetting ruleSetting = userRuleSettingMap.get(todayRuleUser.getRuleId());
            RuleUsers yesterdayRuleUser = yesterdayRuleUserMap.get(todayRuleUser.getRuleId());
            // 消息体通用部分
            String commonInfo = getCommonInfo(todayRuleUser, ruleSetting, volatilityRuleIds);

            // 1. 今日新增圈选(策略创建时间晚于昨天00:00:00)，判断是否超过500W
            if (ruleSetting.getCreateTime().getTime() / 1000L >= TimeUtils.startTime(1)
                    && todayRuleUser.getStatsCount() >= tagConstants.getBigResultThresholdCount()) {
                bigResultRuleErrorMsg.append(commonInfo).append("今日:").append(todayRuleUser.getStatsCount());
                continue;
            }

            if (Objects.isNull(yesterdayRuleUser)) {
                continue;
            }

            // 策略是否配置了相应投放计划（投放重点提醒）并打点
            RecallPlanResponseBO response = assembleRecallPlanMsg(todayRuleUser.getRuleId());

            //Boolean isExistRecallPlan = promoRecallFacade.existRecallPlan(String.valueOf(todayRuleUser.getRuleId()));

            MetricsUtils.logEventWithSpan("monitor_fluctuation_user_rule",
                    "big_data_exist_recall_plan_" + response.getIsExistPlan(),
                    "ruleId:" + todayRuleUser.getRuleId() +
                            ",plan_id:" + response.getLatestPlanId() +
                            ",plan_time:" + response.getLatestPlanTime());

            // 2. 昨天未超过500W，今日超过500W
            if (yesterdayRuleUser.getStatsCount() < tagConstants.getBigResultThresholdCount() && todayRuleUser.getStatsCount() >= tagConstants.getBigResultThresholdCount()) {
                bigResultRuleCount ++;
                bigResultRuleErrorMsgBody.append(commonInfo).append("今日:").append(todayRuleUser.getStatsCount()).append(", 昨日:").append(yesterdayRuleUser.getStatsCount());
                bigResultRuleErrorMsgBody.append(response.getIsExistPlan() ? response.getLatestPlanInfo() : "");

                continue;
            }

            // 3. 结果集数量超过1W且环比昨天波动超 50%(具体数量及比例以配置为准)
            if ((todayRuleUser.getStatsCount() >= tagConstants.getFluctuationThresholdCount() || yesterdayRuleUser.getStatsCount() >= tagConstants.getFluctuationThresholdCount())
                    && Math.abs(todayRuleUser.getStatsCount().doubleValue() / yesterdayRuleUser.getStatsCount().doubleValue() * 100 - 100) >= tagConstants.getFluctuationPercents()) {

                StringBuilder commonyRuleErrorMsgBody = new StringBuilder();
                commonyRuleErrorMsgBody.append(commonInfo).append("今日:").append(todayRuleUser.getStatsCount()).append(", 昨日:").append(yesterdayRuleUser.getStatsCount());

                RuleSetting setting = userRuleSettingMap.get(todayRuleUser.getRuleId());
                if (Objects.nonNull(setting)) {
                    commonyRuleErrorMsgBody.append(", 编辑时间:").append(new SimpleDateFormat("yyyyMMdd HH:mm").format(setting.getLastEditTime()));
                }

                // 需要降噪的策略消息与不需要降噪的策略消息分开发送
                if (volatilityRuleIds.contains(todayRuleUser.getRuleId())) {
                    volatilityRuleCount ++;
                    fluctuationVolatilityRuleErrorMsgBody.append(commonyRuleErrorMsgBody);
                    fluctuationVolatilityRuleErrorMsgBody.append(response.getIsExistPlan() ? response.getLatestPlanInfo() : "");
                } else {
                    fluctuationRuleCount++;
                    fluctuationRuleErrorMsgBody.append(commonyRuleErrorMsgBody);
                    fluctuationRuleErrorMsgBody.append(response.getIsExistPlan() ? response.getLatestPlanInfo() : "");
                }
            }
        }

        // 组装消息
        bigResultRuleErrorMsg.append(bigResultRuleErrorMsgHead).append(String.format(fluctuationRuleTotal, bigResultRuleCount)).append(bigResultRuleErrorMsgBody);
        fluctuationRuleErrorMsg.append(fluctuationRuleErrorMsgHead).append(String.format(fluctuationRuleTotal, fluctuationRuleCount)).append(fluctuationRuleErrorMsgBody);
        fluctuationVolatilityRuleErrorMsg.append(fluctuationVolatilityRuleErrorMsgHead).append(String.format(fluctuationRuleTotal, volatilityRuleCount)).append(fluctuationVolatilityRuleErrorMsgBody);
    }

    private void sendAlarmMsgs(StringBuilder bigResultRuleErrorMsg, StringBuilder fluctuationRuleErrorMsg, int fluctuationRuleCount, StringBuilder fluctuationVolatilityRuleErrorMsg, int volatilityRuleCount) {

        log.info("MonitorRuleResultFluctuationAlertJob.sendAlarmMsgs;bigResultRuleErrorMsg={},fluctuationRuleErrorMsg={},fluctuationVolatilityRuleErrorMsg={},fluctuationRuleCount={},volatilityRuleCount={}",
                bigResultRuleErrorMsg,fluctuationRuleErrorMsg,fluctuationVolatilityRuleErrorMsg,fluctuationRuleCount,volatilityRuleCount);

        int sumFluctuationRuleCount = fluctuationRuleCount + volatilityRuleCount;

        // 新增大圈集提醒（超过配置阈值如5000000）
        weChatWorkService.sendBusinessRobotMDContent(substringIfExceed(bigResultRuleErrorMsg.toString()));

        // 波动提醒（需关注）
        weChatWorkService.sendBusinessRobotMDContent(substringIfExceed(fluctuationRuleErrorMsg.toString()));

        // 易变策略波动提醒（如企微）
        weChatWorkService.sendBusinessRobotMDContent(substringIfExceed(fluctuationVolatilityRuleErrorMsg.toString()));

        // 波动总量提醒
        if (sumFluctuationRuleCount >= tagConstants.getFluctuationAlertCount()) {
            weChatWorkService.sendCheckJobRobotTextContent("用户策略圈选结果集波动过大（超过" + tagConstants.getFluctuationPercents() + "%）的策略达到" + sumFluctuationRuleCount + "个，请确认是否存在问题！！！");

            // 波动数量过多埋点
            MetricsUtils.logEventWithSpan("monitor_rule_result_fluctuation", "too_much",
                    "fluctuation_percents:" + tagConstants.getFluctuationPercents() + "%, fluctuation_cnt:" + sumFluctuationRuleCount);
        }
    }

    private String substringIfExceed(String str) {
        if (StringUtils.length(str) > robotMsgMaxLength) {
            return StringUtils.substring(str, 0, robotMsgMaxLength) + "...";
        }
        return str;
    }

    private String getCommonInfo(RuleUsers todayRuleUser, RuleSetting ruleSetting, List<Long> volatilityRuleIds) {

//        String ruleType = RuleSettingSelectTypeEnum.getMsg(ruleSetting.getSelectType());
//
//        if (CollectionUtils.isNotEmpty(volatilityRuleIds) && volatilityRuleIds.contains(ruleSetting.getId())) {
//            ruleType = tagConstants.getVolatilityTypeDesc();
//        }

        return "\n" + todayRuleUser.getRuleId()
                + "(" + ruleSetting.getName() + "), "
                + "责任人:" + getOwnerName(ruleSetting)
                + ", ";
    }

    private String getOwnerName(RuleSetting ruleSetting) {

        List<String> wechatCoIds = solarClient.getWechatCoIds(Collections.singletonList(ruleSetting.getOwnerEmail()));
        if (CollectionUtils.isNotEmpty(wechatCoIds)) {
            return "<@" + wechatCoIds.get(0) + ">";
        }
        return ruleSetting.getOwnerName();
    }

    private RecallPlanResponseBO assembleRecallPlanMsg(Long ruleId) {

        RecallPlanResponseBO response = new RecallPlanResponseBO(false, "", "", "");

        if (tagConstants.isRecallPlanNewSwitch()) {
            List<UserTagPlanInfoVo> plans = promoRecallFacade.queryRuleRecallPlan(String.valueOf(ruleId));
            if (CollectionUtils.isNotEmpty(plans) && Objects.nonNull(plans.get(0))) {
                response.setIsExistPlan(true);
                UserTagPlanInfoVo latestPlan = plans.get(0);
                response.setLatestPlanId(latestPlan.getPlanId());
                String latestPlanTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(latestPlan.getCurrentExecuteDateTime());
                response.setLatestPlanTime(latestPlanTime);
                String recallMethod = "未知";
                Integer bizType = latestPlan.getBizType();
                if (0 == bizType || 2 == bizType) {
                    recallMethod = "端外";
                } else if (1 == bizType) {
                    recallMethod = "端内";
                }
                response.setLatestPlanInfo("<font color=\"warning\">(" + recallMethod + "召回 " + latestPlanTime + ")</font>");
            }
        } else {
            boolean existRecallPlan = promoRecallFacade.existRecallPlan(String.valueOf(ruleId));
            response.setIsExistPlan(existRecallPlan);
            if (existRecallPlan) {
                response.setLatestPlanInfo("<font color=\"warning\">(用于投放计划)</font>");
            }
        }

        return response;
    }

}
