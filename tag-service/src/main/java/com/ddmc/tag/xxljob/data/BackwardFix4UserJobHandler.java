package com.ddmc.tag.xxljob.data;

import com.ddmc.tag.config.RedisCacheConfig4UserRules;
import com.ddmc.tag.constant.CacheKeyConstants;
import com.ddmc.tag.constant.TagConstants;
import com.ddmc.tag.dao.tag.rule.RuleSettingDao;
import com.ddmc.tag.enums.category.CategoryEnum;
import com.ddmc.tag.infrastructure.rpc.oneapi.OneApiFacade;
import com.ddmc.tag.infrastructure.rpc.oneapi.dto.ExtractUsersDTO;
import com.ddmc.tag.redis.TagRedisUtil;
import com.ddmc.tag.util.MetricsUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Component
public class BackwardFix4UserJobHandler {

    @Resource(name = RedisCacheConfig4UserRules.TAG_REDIS_CLIENT_UTIL_4_USER_RULES)
    private TagRedisUtil tagRedisUtil4UserRules;

    @Resource
    private OneApiFacade oneApiFacade;

    @Resource
    private RuleSettingDao ruleSettingDao;

    @Resource
    private TagConstants tagConstants;

    @XxlJob(value = "BackwardFix4UserJob")
    public ReturnT<String> execute(String param) throws Exception {
        try {
            log.info("BackwardFix4UserJob.execute.start;param={}", param);

            // 查询生效中的策略
            List<Long> allUsingUpdateDailyIds = ruleSettingDao.findAllUpdateDailyIds(CategoryEnum.USER.getValue());
            Set<String> usingRules = allUsingUpdateDailyIds.stream().map(String::valueOf).collect(Collectors.toSet());

            // 分页查询参数
            int pageSize = 100; // 每页大小
            int page = 1;       // 从第 1 页开始

            // 分页循环查询并处理
            while (true) {
                log.info("BackwardFix4UserJob Processing page {}", page);

                // 查询当前页数据 todo 修改为查询指定修复用户逆向的表，
                List<ExtractUsersDTO> extractUsers = oneApiFacade.queryExtractUsersForBackCompare(page, pageSize);
                if (CollectionUtils.isEmpty(extractUsers)) {
                    log.info("BackwardFix4UserJob No more data found at page {}, ending process", page);
                    break; // 如果当前页为空，结束循环
                }

                // 转换为 userRulesMap
                Map<String, Set<String>> userRulesMap = convertToUserRulesMap(extractUsers);
                log.info("BackwardFix4UserJob Page {}: userRulesMap size = {}", page, userRulesMap.size());

                // 循环遍历当前页的 userRulesMap，与 Redis 比对
                userRulesMap.forEach((uid, ruleIds) -> {
                    MetricsUtils.logEventWithSpan("backward_compare_user_fix", "total_cmp", "");
                    if (StringUtils.isBlank(uid) || CollectionUtils.isEmpty(ruleIds)) {
                        log.warn("BackwardFix4UserJob Invalid input; uid={}; ruleIds={}", uid, ruleIds);
                        MetricsUtils.logEventWithSpan("backward_compare_user", "fail_invalid_input", "uid:" + uid + ",ruleIds:" + ruleIds);
                        return;
                    }

                    String cacheKey = CacheKeyConstants.getUserRulesResultByUid(uid);
                    Set<String> allHitRuleIds = tagRedisUtil4UserRules.getAllMembers(cacheKey);
                    if (CollectionUtils.isEmpty(allHitRuleIds)) {
                        log.warn("BackwardFix4UserJob Redis empty; uid={}; ruleIds={}", uid, ruleIds);
                        MetricsUtils.logEventWithSpan("backward_compare_user_fix", "fail_redis_empty", "uid:" + uid + ",ruleIds:" + ruleIds);
                        return;
                    }

                    // 比对正向和逆向的 rule_ids
                    Set<String> onlyForwardRuleIds = new HashSet<>(ruleIds);
                    Set<String> onlyBackwardRuleIds = new HashSet<>(allHitRuleIds);
                    onlyForwardRuleIds.removeAll(allHitRuleIds);
                    onlyBackwardRuleIds.removeAll(ruleIds);
                    boolean cmpPass = true;

                    if (!CollectionUtils.isEmpty(onlyForwardRuleIds)) {
                        // 逆向缺失
                        cmpPass = false;
                        log.warn("BackwardFix4UserJob Compare fail (lack); uid={}; onlyForwardRuleIds={}", uid, onlyForwardRuleIds);
                        MetricsUtils.logEventWithSpan("backward_compare_user_fix", "fail_only_mysql", "uid:" + uid + ",lack_rules:" + onlyForwardRuleIds);
                        if (tagConstants.isUserRuleBackCheckRepairSwitch()) {
                            MetricsUtils.logEventWithSpan("backward_compare_user_fix", "repair_append", "uid:" + uid + ",append_rules:" + onlyForwardRuleIds);
                            tagRedisUtil4UserRules.putSet(cacheKey, onlyForwardRuleIds, 0);
                        }
                    }

                    if (!CollectionUtils.isEmpty(onlyBackwardRuleIds)) {
                        log.warn("BackwardFix4UserJob Maybe fail (extra); uid={}; onlyBackwardRuleIds={}", uid, onlyBackwardRuleIds);
                        MetricsUtils.logEventWithSpan("backward_compare_user_fix", "maybe_fail_extra", "uid:" + uid + ",extra_rules:" + onlyBackwardRuleIds);
                        // 过滤有效的策略
                        Set<String> validRules = onlyBackwardRuleIds.stream().filter(usingRules::contains).collect(Collectors.toSet());
                        if (!CollectionUtils.isEmpty(validRules)) {
                            cmpPass = false;
                            log.warn("BackwardFix4UserJob Compare fail (extra valid); uid={}; valid.onlyBackwardRuleIds={}", uid, validRules);
                            MetricsUtils.logEventWithSpan("backward_compare_user_fix", "fail_only_redis", "uid:" + uid + ",valid_extra_rules:" + validRules);
                            if (tagConstants.isUserRuleBackCheckRepairSwitch()) {
                                MetricsUtils.logEventWithSpan("backward_compare_user_fix", "repair_remove", "uid:" + uid + ",remove_rules:" + onlyForwardRuleIds);
                                tagRedisUtil4UserRules.removeMembers(cacheKey, validRules);
                            }
                        }
                    }

                    log.info("BackwardFix4UserJob Compare result; uid={}; cmpPass={}", uid, cmpPass);
                    MetricsUtils.logEventWithSpan("backward_compare_user_fix", "cmpPass_" + cmpPass, "uid:" + uid);
                });

                page++; // 移动到下一页
            }

            log.info("BackwardFix4UserJob.execute.end");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("BackwardFix4UserJob.execute.exception;param={}; ", param, e);
            return ReturnT.FAIL;
        }
    }

    @Data
    public static class CompareJobParams {
        private Map<String, Set<String>> userRulesMap;
    }

    public Map<String, Set<String>> convertToUserRulesMap(List<ExtractUsersDTO> extractUsers) {
        return extractUsers.stream()
                .filter(dto -> dto.getUserId() != null)  // 过滤掉uid为null的无效数据
                .collect(Collectors.groupingBy(
                        ExtractUsersDTO::getUserId,          // Key: uid
                        Collectors.flatMapping(dto ->     // Value: 拆解ruleIds后的Set
                                        Arrays.stream(dto.getRuleIds().split(","))
                                                .map(String::trim)
                                                .filter(rule -> !rule.isEmpty()),
                                Collectors.toSet()
                        )
                ));
    }

}
