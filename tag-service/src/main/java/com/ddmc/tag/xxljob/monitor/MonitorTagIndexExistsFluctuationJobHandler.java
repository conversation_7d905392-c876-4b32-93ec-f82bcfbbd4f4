package com.ddmc.tag.xxljob.monitor;

import cn.hutool.core.date.format.FastDateFormat;
import com.alibaba.fastjson2.JSON;
import com.ddmc.tag.common.MonitorDataCommonService;
import com.ddmc.tag.common.TagSchemaCommonService;
import com.ddmc.tag.constant.CacheKeyConstants;
import com.ddmc.tag.constant.TagConstants;
import com.ddmc.tag.enums.schema.SchemaTypeEnum;
import com.ddmc.tag.model.schema.TagCategoryFluctuationHistory;
import com.ddmc.tag.pojo.v1.request.job.TagSchemaCountRefreshJobParams;
import com.ddmc.tag.sdk.constant.Constants;
import com.ddmc.tag.service.schema.TagCategoryFluctuationHistoryService;
import com.ddmc.tag.service.wx.WeChatWorkService;
import com.ddmc.tag.util.DateUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 生产环境已停止，测试环境运行中。配置了三条规则，分别针对：商品分仓，商品不分仓，用户。
 *
 * 【生产环境】
 * 4545	标签无值监控波动告警(商品分仓)	BEAN：MonitorTagIndexExistsFluctuationJob	0 20 5-9 * * ?	杨洋	STOP
 * 4544	标签无值监控波动告警(商品不分仓)	BEAN：MonitorTagIndexExistsFluctuationJob	0 20 5-9 * * ?	杨洋	STOP
 * 4543	标签无值监控波动告警(用户)	BEAN：MonitorTagIndexExistsFluctuationJob	0 20 5-9 * * ?	杨洋	STOP
 *
 *
 * 【灰度环境】
 * 4536	标签无值监控波动告警(商品分仓)	BEAN：MonitorTagIndexExistsFluctuationJob	0 20 8-11 * * ?	杨洋	RUNNING
 * 4535	标签无值监控波动告警(商品不分仓)	BEAN：MonitorTagIndexExistsFluctuationJob	0 20 8-11 * * ?	杨洋	RUNNING
 * 4534	标签无值监控波动告警(用户)	BEAN：MonitorTagIndexExistsFluctuationJob	0 20 5-9 * * ?	杨洋	RUNNING
 *
 * 监控ES标签无值数量波动任务
 */
@Slf4j
@Component
public class MonitorTagIndexExistsFluctuationJobHandler {

    @Resource
    private WeChatWorkService weChatWorkService;

    @Resource
    private TagSchemaCommonService tagSchemaCommonService;

    @Resource
    private TagConstants tagConstants;

    @Resource
    private TagCategoryFluctuationHistoryService tagCategoryFluctuationHistoryService;

    @Resource
    private MonitorDataCommonService monitorDataCommonService;

    @XxlJob("MonitorTagIndexExistsFluctuationJob")
    public ReturnT<String> execute(String params) {
        try {
            log.info("MonitorTagIndexExistsFluctuationJob.execute.start;param:{}", params);

            // 0.1 解析参数
            TagSchemaCountRefreshJobParams parseParams = monitorDataCommonService.parseTagSchemaCountRefreshJobParams(params);
            if (Objects.isNull(parseParams) || Objects.isNull(parseParams.getType())
                    || Objects.isNull(parseParams.getIsForce()) || !SchemaTypeEnum.exist(parseParams.getType())) {
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "参数错误");
            }

            // 0.2 判断当日数据是否准备好
            if (!monitorDataCommonService.dataPrepared(parseParams.getType())) {
                return new ReturnT<>(ReturnT.SUCCESS_CODE, SchemaTypeEnum.getDesc(parseParams.getType())
                        + "大数据端今日商品数据未同步");
            }

            // 0.2 今日执行判断&是否强制执行
            Date dateStart = DateUtils.getDayStart(System.currentTimeMillis());
            String dateStr = FastDateFormat.getInstance(DateUtils.YYYYMMDD).format(dateStart);
            if (!parseParams.getIsForce() && monitorDataCommonService.checkIsAlreadyExecute(CacheKeyConstants.TAG_EXISTS_PREFIX,
                    parseParams.getType(), dateStr)) {
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "今日数据已经执行,请勿重复执行");
            }

            // 1. 分类型取出各自top50标签
            List<String> topNTags = tagSchemaCommonService.getTopNTags(tagConstants.getTagTopNWarningCount(), parseParams.getType());
            if (CollectionUtils.isEmpty(topNTags)) {
                log.warn("MonitorTagIndexExistsFluctuationJob.execute.topNTags.is.null;params={}", params);
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "无TopN标签");
            }

            // 2. 比对波动范围 与前一天对比波动超过阈值发出告警
            Map<String, String> warningFluctuationMap = assembleWarningFluctuations(topNTags,
                    tagConstants.getTagExistsWarningFluctuationRate(), parseParams.getType());

            if (MapUtils.isNotEmpty(warningFluctuationMap)) {
                log.info("MonitorTagIndexExistsFluctuationJob.execute.need.warning;warningFluctuationMap={}",
                        JSON.toJSONString(warningFluctuationMap));
                String warningFluctuationContent = assembleWarningFluctuationContent(warningFluctuationMap,
                        tagConstants.getTagExistsWarningFluctuationRate(), parseParams.getType());
                weChatWorkService.sendBusinessRobotTextContent(warningFluctuationContent);
            }

            // 写入执行过的标记
            monitorDataCommonService.writeExecutedFlag(CacheKeyConstants.TAG_EXISTS_PREFIX, parseParams.getType(), dateStr);

            log.info("monitorProductDataJob execute end.param:{}", params);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("MonitorTagIndexExistsFluctuationJob.execute.exception;param={} ", params, e);
            return ReturnT.FAIL;
        }
    }

    private String assembleWarningFluctuationContent(Map<String, String> warningFluctuationMap, Integer fluctuationRate, Integer type) {
        StringBuilder sb = new StringBuilder("今日存在 [" + SchemaTypeEnum.getDesc(type) + "] 无值波动过大(超过" + fluctuationRate + "%) 今日数量-昨日数量对比明细: ");
        warningFluctuationMap.forEach((tag, flu) -> sb.append(tag).append("(").append(flu).append("),"));
        String warningFluctuationContent = sb.toString();
        return substringIfExceed(warningFluctuationContent.substring(0, warningFluctuationContent.length() - 1));
    }

    private Map<String, String> assembleWarningFluctuations(List<String> topNTags, Integer fluctuationRate, Integer type) {
        // 标签名-今日数量&昨日数量Map
        Map<String, String> warningFluctuationMap = new HashMap<>();
        // 查询昨日数据
        List<TagCategoryFluctuationHistory> yesterdayHistories = tagCategoryFluctuationHistoryService
                .selectBySchemaNamesAndOptDate(topNTags, getYesterdayDayDate(), type);

        // 查询今日数据
        List<TagCategoryFluctuationHistory> todayHistories = tagCategoryFluctuationHistoryService
                .selectBySchemaNamesAndOptDate(topNTags, getTodayDate(), type);

        // 数据校验
        if (CollectionUtils.isEmpty(yesterdayHistories) || CollectionUtils.isEmpty(todayHistories)) {
            log.warn("MonitorTagIndexExistsFluctuationJob.assembleWarningFluctuations.histories.is.null;yesterday={},today={}",
                    JSON.toJSONString(yesterdayHistories), JSON.toJSONString(todayHistories));
            return warningFluctuationMap;
        }

        // 昨日最新批次
        Map<String, Integer> yesterdayLatestSchemaCountMap = tagSchemaCommonService.getCurrDayLatestHistories(yesterdayHistories).stream()
                .collect(Collectors.toMap(TagCategoryFluctuationHistory::getSchemaName, TagCategoryFluctuationHistory::getIndexCount));

        // 今日最新批次
        Map<String, Integer> todayLatestSchemaCountMap = tagSchemaCommonService.getCurrDayLatestHistories(todayHistories).stream()
                .collect(Collectors.toMap(TagCategoryFluctuationHistory::getSchemaName, TagCategoryFluctuationHistory::getIndexCount));

        // 进行比对
        topNTags.forEach(tag -> {
            Integer todayCount = todayLatestSchemaCountMap.get(tag);
            Integer yesterdayCount = yesterdayLatestSchemaCountMap.get(tag);

            if (Objects.isNull(todayCount) && Objects.isNull(yesterdayCount)) {
                return;
            }

            if (Objects.isNull(todayCount) || Objects.isNull(yesterdayCount)) {
                warningFluctuationMap.put(tag, todayCount + "-" + yesterdayCount);
                return;
            }

            // 未达到触发告警数量不告警
            if (yesterdayCount < tagConstants.getTagExistsWarningStartValue() && todayCount < tagConstants.getTagExistsWarningStartValue()) {
                log.warn("MonitorTagIndexExistsFluctuationJob.assembleWarningFluctuations.two.days.count.both.below.start.value;tag={}", tag);
                return;
            }

            if (yesterdayCount == 0 && todayCount == 0) {
                log.warn("MonitorTagIndexExistsFluctuationJob.assembleWarningFluctuations.two.days.count.both.0;tag={}", tag);
                return;
            }

            if (yesterdayCount == 0) {
                log.warn("MonitorTagIndexExistsFluctuationJob.assembleWarningFluctuations.yesterdayCount.is.0;tag={}", tag);
                warningFluctuationMap.put(tag, todayCount + "-0");
                return;
            }

            double fluctuationPercent = Math.abs(todayCount.doubleValue() / yesterdayCount.doubleValue() * 100 - 100);
            if (fluctuationPercent < fluctuationRate) {
                return;
            }

            warningFluctuationMap.put(tag, todayCount + "-" + yesterdayCount);
        });

        return warningFluctuationMap;
    }

    private String getTodayDate() {
        Date dateStart = DateUtils.getDayStart(System.currentTimeMillis());
        return FastDateFormat.getInstance(DateUtils.YYYYMMDD).format(dateStart);
    }

    private String getYesterdayDayDate() {
        Date dateStart = DateUtils.getDayStart(System.currentTimeMillis() - (Constants.ONE_DAY_SECONDS * 1000));
        return FastDateFormat.getInstance(DateUtils.YYYYMMDD).format(dateStart);
    }

    private String substringIfExceed(String warningFluctuationContent) {
        if (StringUtils.isBlank(warningFluctuationContent)) {
            return StringUtils.EMPTY;
        }

        if (StringUtils.length(warningFluctuationContent) > tagConstants.getRobotMsgMaxLength()) {
            return StringUtils.substring(warningFluctuationContent, 0, tagConstants.getRobotMsgMaxLength()) + "...";
        }

        return warningFluctuationContent;
    }

}