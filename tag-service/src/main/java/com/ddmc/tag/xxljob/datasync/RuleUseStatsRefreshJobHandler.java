package com.ddmc.tag.xxljob.datasync;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.ddmc.da.oneapi.client.common.OneapiRequest;
import com.ddmc.tag.constant.RuleConstants;
import com.ddmc.tag.constant.TagConstants;
import com.ddmc.tag.dao.tag.rule.RuleSettingDao;
import com.ddmc.tag.dao.tag.stats.StatsRuleInUseDao;
import com.ddmc.tag.enums.rule.RuleStatusEnum;
import com.ddmc.tag.infrastructure.rpc.oneapi.OneApiFacade;
import com.ddmc.tag.model.rule.RuleSetting;
import com.ddmc.tag.model.stats.StatsRuleInUse;
import com.ddmc.tag.pojo.v1.response.stats.RuleUseStatsQueryResponseVO;
import com.ddmc.tag.service.wx.WeChatWorkService;
import com.ddmc.utils.json.JsonUtil;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 【生产环境-运行中】 3842	策略使用数据刷新任务	BEAN：RuleUseStatsRefreshJob	0 0 17 * * ?
 * 【灰度环境-已停止】 3841	策略使用数据刷新任务	BEAN：RuleUseStatsRefreshJob	0 0 17 * * ?
 *
 * 策略使用数据刷新任务 一天跑一次
 */
@Slf4j
@Component
public class RuleUseStatsRefreshJobHandler {

    @Resource
    private OneApiFacade oneApiFacade;

    @Resource
    private StatsRuleInUseDao statsRuleInUseDao;

    @Resource
    private RuleConstants ruleConstants;

    @Resource
    private RuleSettingDao ruleSettingDao;

    @Autowired
    private WeChatWorkService weChatWorkService;

    @Resource
    private TagConstants tagConstants;

    /**
     * 策略使用数据刷新任务
     */
    @XxlJob(value = "RuleUseStatsRefreshJob")
    public ReturnT<String> execute(String param) throws Exception {
        try {
            log.info("RuleUseStatsRefreshJob.RuleUseStatsRefreshJob.execute.start;param={}", param);

            String pDate = new SimpleDateFormat("yyyy-MM-dd").format(Date.from(LocalDate.now().minusDays(1)
                    .atStartOfDay(ZoneId.systemDefault()).toInstant()));

            RuleUseStatsRefreshJobHandler.RuleUseStatsRefreshJobParams jobParams = JsonUtil.jsonToBean(param,
                    RuleUseStatsRefreshJobHandler.RuleUseStatsRefreshJobParams.class);
            if (!Objects.isNull(jobParams)) {
                pDate = jobParams.getPDate();
            }

            log.info("RuleUseStatsRefreshJob.RuleUseStatsRefreshJob.execute.start;param={},pDate={}", param, pDate);

            List<RuleUseStatsQueryResponseVO> ruleUseStats = rpcGetRuleUseStats(pDate);
            if (CollectionUtils.isEmpty(ruleUseStats)) {
                log.warn("RuleUseStatsRefreshJob.RuleUseStatsRefreshJob.rpc.ruleUseStats.is.null;pDate={}", pDate);
                return ReturnT.FAIL;
            }

            // 聚合数据
            List<StatsRuleInUse> statsRuleInUses = assembleRuleUseStats(ruleUseStats);

            // 保存或修改
            if (CollectionUtils.isEmpty(statsRuleInUses)) {
                log.warn("RuleUseStatsRefreshJob.RuleUseStatsRefreshJob.assembleRuleUseStats.is.null;");
                return ReturnT.FAIL;
            }

            List<List<StatsRuleInUse>> partition = Lists.partition(statsRuleInUses, ruleConstants.getRuleUseStatsPartitionCount());
            for(List<StatsRuleInUse> part : partition){
                // 分批次落表
                statsRuleInUseDao.saveOrUpdateBatch(part);
            }

            log.info("RuleUseStatsRefreshJob.RuleUseStatsRefreshJob.execute.end;ruleIds={}",
                    statsRuleInUses.stream().map(StatsRuleInUse::getRuleId).collect(Collectors.toList()));

            // 策略有使用上报但状态不为生效中告警
            alertCalledButNotUsingStatus(statsRuleInUses);

        } catch (Exception e) {
            log.error("RuleUseStatsRefreshJob.RuleUseStatsRefreshJob.execute.exception;param={}; ", param, e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    private void alertCalledButNotUsingStatus(List<StatsRuleInUse> statsRuleInUses) {
        try {
            Set<Long> ruleIds = statsRuleInUses.stream().map(StatsRuleInUse :: getRuleId).collect(Collectors.toSet());
            List<RuleSetting> ruleSettingList = ruleSettingDao.queryPageByRuleId(ruleIds,0L, (long) ruleIds.size());
            List<RuleSetting> notUsingRules = ruleSettingList.stream().filter(ruleSetting -> !Objects.equals(ruleSetting.getRuleStatus(), RuleStatusEnum.USING.getValue())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(notUsingRules)) {
                StringBuilder alertSB = new StringBuilder("【以下策略有上报，但策略状态不为生效中】具体如下：");
                // 组装并发送发企微告警消息
                for (RuleSetting ruleSetting : notUsingRules) {
                    alertSB.append("\n").append(ruleSetting.getId()).append("(").append(ruleSetting.getName()).append(")，责任人:").append(ruleSetting.getCreatorName()).append(";");
                }
                weChatWorkService.sendProductBusinessRobotTextContent(alertSB.toString());
            }
        } catch (Exception e) {
            log.error("alertCalledButNotUsingStatus exception e", e);
        }
    }

    private List<StatsRuleInUse> assembleRuleUseStats(List<RuleUseStatsQueryResponseVO> ruleUseStats) {
        List<StatsRuleInUse> statsRuleInUses = new ArrayList<>();
        ruleUseStats.forEach(ruleUseStat -> {
            StatsRuleInUse statsRuleInUse = new StatsRuleInUse();
            statsRuleInUse.setRuleId(ruleUseStat.getRuleId());
            statsRuleInUse.setLatestUseTime(ruleUseStat.getLatestUseTime());
            statsRuleInUse.setRuleUseCnt(ruleUseStat.getRuleUseCnt());
            statsRuleInUse.setCreateTime(new Date());
            statsRuleInUse.setUpdateTime(new Date());
            statsRuleInUses.add(statsRuleInUse);
        });
        return statsRuleInUses;
    }

    private List<RuleUseStatsQueryResponseVO> rpcGetRuleUseStats(String pdate) {
        List<RuleUseStatsQueryResponseVO> responseVOS = new ArrayList<>();
        int pageNum = 1;
        int resultMapSize = 0;
        while ((pageNum == 1) || resultMapSize > 0) {
            // WTF, why one-api client declare a Object from 3rd jar？
            com.alibaba.fastjson.JSONObject jsonObject = new JSONObject();
            jsonObject.put("pdate",pdate);
            OneapiRequest.Request oneapiWithSecretRequest = new OneapiRequest.Request();
            oneapiWithSecretRequest.setParamData(jsonObject);
            oneapiWithSecretRequest.setPageSize(ruleConstants.getOneApiRuleStatsPageSize());
            oneapiWithSecretRequest.setPageNum(pageNum);
            oneapiWithSecretRequest.setApiCode(ruleConstants.getOneApiRuleStatsCode());
            List<Map<String, Object>> resultMapList = oneApiFacade.queryResultList(oneapiWithSecretRequest);
            log.info("RuleUseStatsRefreshJobHandler.rpcGetRuleUseStats.end;result={},pdate={}", JSON.toJSONString(resultMapList), pdate);
            resultMapList.forEach(resultMap -> {
                try {
                    RuleUseStatsQueryResponseVO responseVO = new RuleUseStatsQueryResponseVO();
                    Object ruleIdObj = resultMap.get("ruleId");
                    if (!Objects.isNull(ruleIdObj)) {
                        responseVO.setRuleId(Long.valueOf(String.valueOf(ruleIdObj)));
                    }
                    Object latestUseTimeObj = resultMap.get("latestUseTime");
                    if (!Objects.isNull(latestUseTimeObj)) {
                        responseVO.setLatestUseTime(new SimpleDateFormat("yyyy-MM-dd").parse(String.valueOf(latestUseTimeObj)));
                    }
                    Object ruleUseCntObj = resultMap.get("ruleUseCnt");
                    if (!Objects.isNull(ruleUseCntObj)) {
                        responseVO.setRuleUseCnt(Long.valueOf(String.valueOf(ruleUseCntObj)));
                    }
                    if (Objects.isNull(responseVO.getRuleId()) || Objects.isNull(responseVO.getLatestUseTime())
                            || Objects.isNull(responseVO.getRuleUseCnt())) {
                        return;
                    }
                    responseVOS.add(responseVO);
                } catch (Exception e) {
                    log.error("RuleUseStatsRefreshJobHandler.rpcGetRuleUseStats.result.transfer.exception;resultMap={},e={}", resultMap,
                            ExceptionUtils.getStackTrace(e));
                }
            });
            pageNum++;
        }
        return responseVOS;
    }

    @Data
    public static class RuleUseStatsRefreshJobParams {
        private String pDate;
    }

}
