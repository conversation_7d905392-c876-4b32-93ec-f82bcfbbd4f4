package com.ddmc.tag.xxljob.monitor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.ddmc.tag.config.RedisCacheConfig4Rule;
import com.ddmc.tag.constant.CacheKeyConstants;
import com.ddmc.tag.constant.TagConstants;
import com.ddmc.tag.dao.tag.rule.RuleSettingDao;
import com.ddmc.tag.dao.tag.rule.RuleSettingDslDao;
import com.ddmc.tag.dao.tag.stats.RuleUsersDao;
import com.ddmc.tag.elasticsearch.EsUtils4Bigdata;
import com.ddmc.tag.enums.category.CategoryEnum;
import com.ddmc.tag.enums.rule.RuleSettingSelectTypeEnum;
import com.ddmc.tag.enums.rule.RuleSettingUpdateTypeEnum;
import com.ddmc.tag.enums.rule.RuleStatusEnum;
import com.ddmc.tag.model.rule.RuleSetting;
import com.ddmc.tag.model.rule.RuleSettingDsl;
import com.ddmc.tag.model.stats.RuleUsers;
import com.ddmc.tag.redis.TagRedisUtil;
import com.ddmc.tag.service.wx.WeChatWorkService;
import com.ddmc.tag.util.LogUtil;
import com.ddmc.tag.util.MetricsUtils;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
public class MonitorLoadMonthDroolsAndESCountJobHandler {

    private static Logger log = LoggerFactory.getLogger(MonitorLoadMonthDroolsAndESCountJobHandler.class);

    @Autowired
    private WeChatWorkService weChatWorkService;

    @Autowired
    private EsUtils4Bigdata esUtils4Bigdata;

    @Autowired
    private RuleSettingDao ruleSettingDao;
    @Autowired
    private RuleSettingDslDao ruleSettingDslDao;
    @Autowired
    private RuleUsersDao ruleUsersDao;

    @Autowired
    @Qualifier(RedisCacheConfig4Rule.TAG_REDIS_CLIENT_UTIL_4RULE)
    private TagRedisUtil ruleRedisUtil;

    @Autowired
    private TagConstants tagConstants;

    @Value("${tag.monitor.ruletargetcount.escount.thread.pool.corePoolSize:8}")
    private int esCountThreadPoolCorePoolSize;
    @Value("${tag.monitor.ruletargetcount.escount.thread.pool.maxPoolSize:8}")
    private int esCountThreadPoolMaxPoolSize;
    @Value("${tag.monitor.ruletargetcount.escount.thread.pool.queueCapacity:1000}")
    private int esCountThreadPoolQueueCapacity;

    @SuppressWarnings("unchecked")
    @XxlJob("MonitorLoadMonthDroolsAndESCountJob")
    public ReturnT<String> monitorRuleTargetCountJob(String param) throws Exception {
        LogUtil.info(log, "MonitorRuleTargetCountJob execute start.param:{}", param);

        String dateStr = DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN);

        // 比对完成标记
        if(!check(dateStr)){
            return new ReturnT(ReturnT.SUCCESS_CODE, "今日比对已经完成,请勿重复执行");
        }

        // 今日数据flag check
        if (esNotOk(dateStr)) {
            return new ReturnT(ReturnT.FAIL_CODE, "今日数据还未准备好！！");
        }

        // 今日圈选执行是否完成check (通过当天执行中的用户圈选执行数量是否为0)
        if (todayLoadMonthNotFinished()) {
            return new ReturnT(ReturnT.SUCCESS_CODE, "今日圈选还未完成");
        }

        // 开始比对
        // 获取所有的用户策略
        List<RuleSetting> userRuleSettings = ruleSettingDao.findByCategoryId(CategoryEnum.USER.getValue());

        if(CollectionUtil.isEmpty(userRuleSettings)) {
            String msg = "没有符合条件的用户策略！";
            log.error(msg);
            weChatWorkService.sendCheckJobRobotTextContent(msg);

            return new ReturnT(ReturnT.FAIL_CODE, msg);
        }

        // 比对白名单（可忽略不进行比对）
        List<Long> droolsEsWhiteRuleIds = tagConstants.getDroolsEsWhiteRuleIds();
        log.info("# monitor drools es; droolsEsWhiteRuleIds={}", droolsEsWhiteRuleIds);

        // 只监控例行的且正常的策略
        // 1028增加 不含实时标签 逻辑
        userRuleSettings = userRuleSettings.stream().filter(setting ->
                RuleSettingUpdateTypeEnum.UPDATETYPE_ROUTINE.getValue().intValue() == setting.getUpdateType()
                && RuleStatusEnum.USING.getValue().intValue() == setting.getRuleStatus()
                && setting.getIsIncludeRealTimeTag() == 0
                && RuleSettingSelectTypeEnum.SELECT_TYPE_BY_TAG.getValue().equals(setting.getSelectType())
                && (CollectionUtils.isEmpty(droolsEsWhiteRuleIds) || !droolsEsWhiteRuleIds.contains(setting.getId()))
        ).collect(Collectors.toList());

        List<Long> ruleIds = userRuleSettings.stream().map(RuleSetting::getId).collect(Collectors.toList());

        log.info("# monitor drools es; needCompareIds={}", ruleIds);

        List<RuleSettingDsl> ruleSettingDsls = ruleSettingDslDao.findByRuleIds(ruleIds);

        if (CollUtil.isEmpty(ruleSettingDsls)) {
            String msg = "用户策略对应的es查询语句为空！";
            log.error(msg);
            weChatWorkService.sendCheckJobRobotTextContent(msg);

            return new ReturnT(ReturnT.FAIL_CODE, msg);
        }

        String today = DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN);

        List<RuleUsers> ruleUsersList = ruleUsersDao.queryLatestRuleUsersByRuleIdsAndDate(ruleIds, today);

        Map<Long, Integer> droolsCountMap = Maps.newHashMapWithExpectedSize(ruleUsersList.size());
        ruleUsersList.forEach(ruleUsers -> droolsCountMap.put(ruleUsers.getRuleId(), ruleUsers.getStatsCount()));

        Map<Long, Long> dslCountMap = Maps.newHashMapWithExpectedSize(ruleSettingDsls.size());

        ThreadPoolExecutor esCountExecutor = new ThreadPoolExecutor(
                esCountThreadPoolCorePoolSize,
                esCountThreadPoolMaxPoolSize,
                3,
                TimeUnit.SECONDS, new LinkedBlockingQueue(esCountThreadPoolQueueCapacity),
                new ThreadFactoryBuilder().setNameFormat("esCount-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());

        CountDownLatch countDownLatch = new CountDownLatch(ruleSettingDsls.size());
        ruleSettingDsls.forEach(ruleSettingDsl ->
            esCountExecutor.execute(()->{
                dslCountMap.put(ruleSettingDsl.getRuleId(), esUtils4Bigdata.countUserProfile(ruleSettingDsl.getPlainRuleDsl()));
                countDownLatch.countDown();
            }));

        try {
            countDownLatch.await();
        }catch (Exception e){
            log.error("esCountExecutor countDownLatch await error.", e);
        }

        List<String> errorRuleIds = new ArrayList<>();
        List<String> zeroRuleIds = new ArrayList<>();
        userRuleSettings.forEach(ruleSetting -> {
            Long ruleId = ruleSetting.getId();
            Integer drools = droolsCountMap.get(ruleId);
            Long dsl = dslCountMap.get(ruleId);
            if (drools == null || dsl == null || drools != dsl.intValue()) {
                errorRuleIds.add("\n" + ruleId + "(组id:" + ruleSetting.getGroupId() + "): " + drools + " - " + dsl);
            }
            if(drools != null && dsl != null && drools == 0 && dsl.intValue() == 0){
                zeroRuleIds.add("\n" + ruleId + "(" + ruleSetting.getName() + ")" + "(组id:" + ruleSetting.getGroupId() + ")");
            }
        });

        log.error("#校验数据统计结果：{}", CollUtil.isEmpty(errorRuleIds) ? "成功！" : errorRuleIds);
        weChatWorkService.sendCheckJobRobotTextContent(errorRuleIds);

//        weChatWorkService.sendZeroRuleRobotTextContent(zeroRuleIds);
/*
        int shouldMonitorCount = userRuleSettings.size();
        int droolsCount = droolsCountMap.size();
        int dslCount = dslCountMap.size();

        StringBuilder sb = new StringBuilder();

        sb.append("统计信息：\n");
        if (shouldMonitorCount == droolsCount && droolsCount == dslCount) {
            sb.append("需要监控的用户策略条数相等：").append(shouldMonitorCount).append("\n");
        } else {
            sb.append("需要监控的用户策略条数不等！\n")
                    .append("例行用户策略总数：").append(userRuleSettings.size()).append("\n")
                    .append("drools执行结束的策略数：").append(droolsCountMap.size()).append("\n")
                    .append("dsl执行的策略数：").append(userRuleSettings.size()).append("\n");
        }

        sb.append("\n\n");

        int failCount = 0;
        for (RuleSetting ruleSetting : userRuleSettings) {
            Integer drools = droolsCountMap.get(ruleSetting.getId());
            Long dsl = dslCountMap.get(ruleSetting.getId());

            if (drools == null || dsl == null || drools != dsl.intValue()) {
                failCount ++;
                sb.append("策略：").append(ruleSetting.getName()).append("【")
                        .append(ruleSetting.getId()).append(ruleSetting.getGroupId()).append("】")
                        .append("，drools结果：").append(drools).append("，预期结果：").append(dsl).append("\n");
            }
        }

        if (failCount == 0) {
            sb.append("所有例行用户策略的统计结果与预期相等！").append("\n");
        } else {
            sb.append("例行用户策略的统计结果与预期不相等的个数：").append(failCount).append("\n");
        }

        weChatWorkService.sendCheckJobRobotTextContent(sb.toString());
*/

        // 写比对任务执行完成标记
        ruleRedisUtil.incrOne(CacheKeyConstants.getToDayLoadMonthMonitorJobKey(dateStr));

        // 有比对失败情况失败埋点
        if (CollectionUtils.isNotEmpty(errorRuleIds)) {
            MetricsUtils.logEventWithSpan("monitor_rule_selection_result", "drools_es_not_match",
                    "cnt:" + errorRuleIds.size());
        }

        LogUtil.info(log, "MonitorRuleTargetCountJob execute end.param:{}", param);

        return ReturnT.SUCCESS;
    }

    private boolean esNotOk(String dateStr){
        return ruleRedisUtil.getValue(CacheKeyConstants.getToDayLoadMonthJobTagKey(dateStr)) == null;
    }

    private boolean check(String dateStr){
        return ruleRedisUtil.getValue(CacheKeyConstants.getToDayLoadMonthMonitorJobKey(dateStr)) == null;
    }

    private boolean todayLoadMonthNotFinished() {
        // 查询执行中的用户离线圈选策略数量
        return ruleSettingDao.findAllDailyExecutingRulesCount(CategoryEnum.USER.getValue()) > 0;
    }

}