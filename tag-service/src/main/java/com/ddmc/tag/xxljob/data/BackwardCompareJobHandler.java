package com.ddmc.tag.xxljob.data;

import com.ddmc.tag.config.RedisCacheConfig4Api;
import com.ddmc.tag.constant.CacheKeyConstants;
import com.ddmc.tag.constant.TagConstants;
import com.ddmc.tag.dao.tag.log.AdminOperationLogDao;
import com.ddmc.tag.dao.tag.rule.RuleSettingDao;
import com.ddmc.tag.dao.tag.stats.RuleUsersDao;
import com.ddmc.tag.enums.ValidEnum;
import com.ddmc.tag.enums.category.CategoryEnum;
import com.ddmc.tag.enums.rule.RuleSceneEnum;
import com.ddmc.tag.enums.rule.RuleSettingSelectTypeEnum;
import com.ddmc.tag.enums.rule.RuleSettingUpdateTypeEnum;
import com.ddmc.tag.enums.rule.RuleStatusEnum;
import com.ddmc.tag.model.rule.RuleSetting;
import com.ddmc.tag.model.stats.RuleUsers;
import com.ddmc.tag.redis.TagRedisUtil;
import com.ddmc.tag.sdk.constant.Constants;
import com.ddmc.tag.sdk.mongo.model.RuleProductsByStationDetail;
import com.ddmc.tag.sdk.proxy.RuleProductsByStationDaoProxy;
import com.ddmc.tag.service.base.TagVersionService;
import com.ddmc.tag.util.CodeUtil;
import com.ddmc.tag.util.MetricsUtils;
import com.ddmc.tag.util.WaterfallProcessInterface;
import com.ddmc.utils.json.JsonUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.*;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 用户抽检失败策略大面积修复任务
 */
@Slf4j
@Component
public class BackwardCompareJobHandler {

    private static Set<String> allStationIds = Sets.newHashSet("5c42f1b0d033a1e73c8b4568","5fbb1e30e820e30001ef080c","5be3aae5716de100468c17c6","5ca1ea82d033a1925b8b4582","61ea41ea5f5f3f00018f9dc3","5d00a6d425e1f7f5498b456c","5de87fd726c3d12d538b4569","5bf2903c716de100468c6ca0","5b50430dc0a1eac91e8b4bc2","60347616c6d90100013dc2c9","5f924f9a25f67000014b78e4","5c8879bc716de1f6468b456c","61c2bd74447b190001cbddec","61ea4236f7ed6a00015ed28d","668f4b69b81169002b4e2736","5bf29019716de100468c6c9e","5c9832d1d033a1925b8b456d","61c2c0d125e1f76a628b4568","61357d55fc19ea000156b8f3","668b62e4b81169002b4e2735","5c8879d1716de1f6468b456d","666c260ce93798002cb3111a","61c2bcba5fc1130001701e49","5b779d9f01a6eaaf048b9eb7","5c8879e6716de1f6468b456e","5fb7682be820e30001ef080b","5c04bdbc716de1403a8b679a","5fa8d77af71bcd00018820ab","600178d325e1f71a638b4567","5e94370a26c3d175768b4570","5fa39acb91c66a0001b14fff","5efc978526c3d1f10c8b4567","615e63d4744bef0001377f36","61c2bc721b069600019658da","60db5850bd8d8c0001eca0d8","5aefebdec3c442b62e8cfbf7","5f16519483c4e900012e2edb","5f97924f10dda50001a7ac81","5eb21e332887858c528b456b","5ef4c476288785ff608b4567","66fc0a2fd63482003c08fa5e","5c2f3a3b716de13e768b9ca1","60bf079766874500016623c8","5b504287c0a1eac91e8b4bc1","5cda2209716de103418b456b","5cb00a55d033a1d82a8b456a","5f744a0bba782b0001f2cd54","6176477c8782cc00018b4140","5f56323b2887855c128b4567","609161a32d436c0001c66cd5","5dc38a1e26c3d171698b456d","61133384fafdd600011d3daa","5fa52e42239fe40001fe437b","5be3aafd716de100468c17c7","5ff7c739e820e300017b9119","6100c14fb5871d00015f26d1","60bae2b971345800018b0c66","6683726db81169002b4e2733","66b187851614f20029268032","60ac969e71345800018adaf5","5c1f049e716de13e768b4f17","61c2bc04cd02a900018e9303","5c2f3aed716de13e768b9cbd","6755996ec5dfdc003e8d5b95","5bf2902b716de100468c6c9f","5b779d8101a6eaaf048b9eb6","5c2f3b00716de13e768b9cbe","5fae2ba0ae9c150001442059","61a9931abfc4ae0001c17c93","5f277f3a83c4e90001cd8ef7","5d9e92be26c3d137758b456a","6178fb863e0c7c0001ceefff","67356fa42a1b9a002b12d31b","60f52df77bfef80001781ce6","6513d00485e109001f9fefff","5ee982bf288785661f8b456a","6058645500352f0001ad62f2","5fa52e7e25e1f7a04c8b456a","60be16bc75474200016f2fdf","5c9de650d033a1925b8b457e","5d52966926c3d1af2f8b456b","5ca468afd033a1925b8b4588","5b1f75e806752e5f408b5f70","5b6aa0d9c0a1ea333a8b70e0","604ec3f525e1f7834f8b4567","5c1f0503716de13e768b4f1a","5bbc90d7d033a19c678bb029","5ce50c8825e1f786708b456c","5c0f2468716de1e77d8b4a6d","5c987ec0d033a1925b8b457d","666c25f8d18325002b3ca7e4","5d5296dc26c3d1af2f8b456d","5d2e939426c3d1506a8b4568","5cf100fd26c3d12d278b4579","5ca46818d033a1925b8b4584","5c04bda4716de1403a8b6799","5b98842bc0a1ea8f1c8b5a6a","5d52962726c3d1af2f8b4569","5cc7ad4d716de1c6058b456e","5dc91cbd25e1f7987e8b4567","5cc5b05f716de1c6058b456a","5f9a3b8b3dd12300014d106c","5f16514125e1f7c9058b4567","5c98326ad033a1925b8b4568","5d7f4fe625e1f755198b456b","6091639580a9b900018db6a0","6097dcf9d57a270001630801","6097dc05236f4500011f99ef","5f1651d793e0310001d360ab","6177aae4b547af0001ac0c4c","5ff67df210a3440001ff23e1","60a617f02e1756000156023f","5d52973126c3d1af2f8b4570","5f1548fd40ac940001899af1","610c9fc03ad6820001671410","5f5f48143ed172000175696b","5c3867d0d033a128618b456a","5f681d4625e1f7c34b8b4567","5c1f04bd716de13e768b4f18","5c987e60d033a1925b8b457a","60e555d3a7b4f400012bec4d","603c8732ec9409000134f0cc","600fcaa6288785c26b8b4568","5b779d6301a6eaaf048b9eb5","5f65dd593ed1720001756985","5f587e1926c3d118458b4567","6091627626c3d1487d8b4567","5b779dd201a6eaaf048b9eb8","5f90f1a925f67000014b78e2","5bf28fcd716de100468c6c9a","5ce7498a25e1f7d35e8b4567","5d84724525e1f72b2e8b456b","5fa39b1d57597600010e7959","60e7b27929d9770001b25baf","5cd65e78716de103418b4568","64532ab673f4d1001d636a47","66aaf1211614f20029268031","5c88b7a4716de1f6468b4573","6116511d81ff03000136ac05","5c04be20716de1403a8b679f","5f1ea6813f34a90001d0b0f5","5f990b3e167c260001aa390a","5d0cad8026c3d1811a8b4567","5e7d6d9725e1f7656a8b456c","6102976f13d85c00011a3164","5fa8d74391c66a0001b15101","5cda202d716de103418b4569","60f4e0738d39d9000118d2a3","6440d8dcdd203e001ef383f3","5e9e9eeb28878574728b456a","5a1e13e8916edfa9508f79c9","5db7d5bc25e1f773298b4567","5d1ebb1325e1f78b588b456f","5c6bcbc5716de181098b4567","5ce26ea5716de1db018b4567","5d7233d626c3d161348b456a","5c9832e6d033a1925b8b456e","611c799293bf0e000142f486","59e5b54b916edf47638f895d","5b8d22e2c0a1ea3a278b8b7b","5c98327fd033a1925b8b4569","5e689e6f25e1f711298b4567","60bf0648a0378d0001621547","5fa8da1925e1f7a04c8b456e","5dae5f9825e1f7a4498b456d","5f858cb3719c6c0001f8b483","607531997e8a9d00010ec2bb","5e01a9bf25e1f7f46a8b4567","5fa8da8591c66a0001b15104","5df8d93826c3d198128b4569","5c987e7ed033a1925b8b457b","61ea44a67bfef80001bc054f","60decc0725e1f7595e8b4569","5cf0fae426c3d12d278b456d","5ccadf14716de1c6058b4570","6098f63ca0378d00013180f2","5e7d6fa925e1f7656a8b4572","674ffaa68b0527002d5b5593","5cf0fca426c3d12d278b4570","5a1e17a5916edfa9508fca12","668f8561447158002aed1d6b","66b9c05802d240002b530256","6324643a78a73c001f9d7206","5bda6a6c716de100468be812","5f277edc25e1f7e51d8b4567","5f3a18cf3ed17200017568c8","5cf0fdc926c3d12d278b4573","6168008b2149230001fc635f","61ea476db5871d000144341f","60f4e08c7bfef80001781ce1","5f92541c0af5c200011ae982","60331c3690549400017ce59f","5cfdfd3c25e1f7f5498b4568","60d146cd1200140001355960","5a1e1538916edf42248e50ab","619702b8079813d9608b4567","5d72343f26c3d161348b456c","61ea42b27bfef80001bc054d","5f817353cd6a4e00016c0625","5bc5a8e7716de1a94f8b6fbb","5fd6d60f57d52700014aaf8d","5bf28f78716de100468c6c96","6100c12e13d85c00011a315c","5d86c76425e1f72b2e8b456e","5d9e926426c3d137758b4567","66fc0a65918f86002a5b49aa","5cfdfd6025e1f7f5498b4569","5fb4bb711bdf020001f52a2a","5f92550e25f67000014b78e5","66713d04e93798002cb3111d","5f44a5af3ed17200017568f5","5e7d6d2625e1f7656a8b4567","5aefd021c5702e08048bf23d","605d5ed26a8a5e00017151d1","612dcce08ec0250001078413","609b832a603eb200015de8a7","647fee39e33b6b001d105edf","5ccadfac716de1c6058b4576","5c7de940d033a1c65c8b456a","5b779d3e01a6eaaf048b9eb4","5ccadfc1716de1c6058b4577","5d2d6a7125e1f74c378b456b","5fbcdab6642a1e0001f114d2","60cc0dcf2df0c70001523ccd","5e7d70ff25e1f7656a8b4577","66dcd5e1d63482003c08fa52","5d52979a26c3d1af2f8b4572","5c04bdd0716de1403a8b679b","602e5ab3b48a7400012c7065","5fe302e92056490001f1a7ec","60f7e389401bdd0001d0e4af","5bceb91c716de1b86d8b6168","66e24bd7918f86002a5b49a1","5c7dea0e716de1fc708b456d","5f1378ab93e0310001d3609d","5cf08a8726c3d12d278b456a","60bf046aa8167400013b64f2","5f858a18719c6c0001f8b482","5ee982ec288785661f8b456c","5ec7a23326c3d1cf748b456b","5e7c99402887853a598b4568","602e5a18ec9409000134f0c9","5f9252f092d2d10001fa2b95","5d4a342726c3d1bf738b4569","5eddebe226c3d12b1a8b456d","5d2d6abb25e1f74c378b456c","5f9911b981b78d0001549ae5","5c7de90ed033a1c65c8b4568","5fa39c3dcd6a4e000142309e","5d84716925e1f72b2e8b4567","5edde95126c3d12b1a8b456a","61c2bfb017e2c400010c5b6e","5f1ea7b226c3d13f2f8b4567","66a745751614f2002926802d","5c6bcc9c716de181098b456e","5dcbb6e325e1f7987e8b456c","6097c8ee2657b2000114067f","5f44a56e3ed17200017568f4","5c2f3ac1716de13e768b9cbb","5f9798308bff5b00019ff20f","5d78748025e1f755198b4568","60d146e84229c700018bad22","5d00a80b25e1f7f5498b4575","5fc9a1117193bb0001152637","606bc05b1b770d000164ce8b","5ff7c55a3f34a900015bdedc","60aca27e25e1f7370f8b4568","62e740572edafb001da35dea","5ccadfee716de1c6058b4579","5cb96a98d033a172168b456d","60f7e356401bdd0001d0e4ae","60ed078ba7b4f400012bec63","5e7d6d8725e1f7656a8b456b","5b5043cdc0a1eac91e8b4bc4","663e2ff90533dc00420f7723","5d9e92d826c3d137758b456b","5fa8ae363f34a90001c3be4a","5c7de978716de1fc708b4567","605865cf00352f0001ad62f3","5c983295d033a1925b8b456a","5c983311d033a1925b8b4570","60750e7b337c8b0001ae960d","5c7912a8d033a197258b4571","5fa8d8583f34a90001c3be4e","60bf06c0a0378d0001621548","63bbedac7bc878001dc73a7f","66852e59b81169002b4e2734","60bae1fb24bfea0001a4df46","5cace7eb716de1465c8b4567","5f97949b8bff5b00019ff20e","6673e488447158002aed1d5e","5bbc910bd033a19c678bb02c","5fa8d6fb3f34a90001c3be4c","675be7e55543450028e2d8d5","5c42f1c4d033a1e73c8b4569","5e041fed26c3d11c488b4567","5a4f5c1e916edf83738c399b","5f1ea6263f34a90001d0b0f4","61ea4563f7ed6a00015ed28e","5d1ebb4b25e1f78b588b4570","6104ee25b7e3530001579866","609fab46cfac8e00017cdf13","664eab293b43300043e1b4df","67b557e52391bb003cb8086e","5c8879fe716de1f6468b456f","5e689e8c25e1f711298b4568","57ce9beb916edf4b06eeb86f","60e5610b8cf3960001d3ff39","67b59cace8ed93002ca9bd09","5f8d0aeb8e72da0001e47304","5fb27019be377b0001b042ed","5a4f5adb846c2e437e8dce28","5fa39c0f57597600010e795c","5fa0c69857597600010e7923","668e1200447158002aed1d68","5ea4ed5825e1f7d71e8b4568","5dc0d05226c3d171698b456a","5d358a9f25e1f7597e8b4567","611b764413d85c00011a63d9","5f50b2f95f5f3f000188060c","5f1ea82f26c3d13f2f8b4568","665f3a6e32f8ad002b20007f","615e637cbdd59700015d1a46","5f229dab3f34a90001d0b11a","613ec2133ad6820001794e74","5e12a29326c3d198518b4567","5bc5a846716de1a94f8b6fb8","5b1f61fe06752e3d0a8d5503","5f8d08938e72da0001e47303","659641504c5b51001fb59859","6151a98c744bef0001377f21","5fc9a1b2337c8b000187b064","60decbbcfc19ea00014d7e36","56f35796916edf495eae7e72","5b5043f7c0a1eac91e8b4bc5","674520250bdbd7002606b337","5c7911cfd033a197258b4568","5fc9a099188386000146aa90","5ed5c64826c3d157358b4569","60e13c177bb5150001227d12","5d1ab32625e1f78b588b456b","5d2d619b25e1f74c378b4568","59e5bf6f916edf997a8f8f2e","5d358b8625e1f7597e8b4569","6734619d2a1b9a002b12d319","5fa8acddf71bcd00018820aa","5f68470e5f5f3f000188066e","60db583e63c2ca00013d93f6","5fa39b5757597600010e795a","5fbb4ebee820e30001ef080d","5c6bcc47716de181098b456b","5c04be0c716de1403a8b679e","5c7911edd033a197258b4569","5e87de0c26c3d175768b4568","61029847b7e3530001579865","5fd4603e28d6b30001452821","5e7d6ee425e1f7656a8b456d","5f563265cd6a4e00012217d8","602e5a8ac6d90100013dc2b1","5d7f526625e1f755198b456d","60916300a0378d0001317aba","5aefe719c3c442d1588d1f07","5500fe01916edfe0738b4e43","60db5876ee057e0001c7bfe6","613ec05923aa010001668727","60e579c163c2ca00018a9a23","66cdb2721614f2002926803f","5cdcd6cfd033a1d13f8b456c","673b1d082a1b9a002b12d31d","5d5297f126c3d1af2f8b4575","674823e30bdbd7002606b33b","5f366b8925e1f700588b4567","5d8a03a525e1f7de748b4567","5fb3411e25e1f7866d8b4567","60a8ee302df0c70001481c58","5be3ad27716de100468c17ca","5f27f1fb83c4e90001cd8ef8","607cfa6697b2520001b9514f","5fc4c44d28878580748b4569","605863cc4e98ab0001317646","60916134cfac8e00017cd8d4","5ea9575725e1f71e518b456b","61318050fc19ea000156b8ec","59e5bd12916edf56028f43c3","5f27807b2a331e0001051732","5c3be52d716de1416b8b456f","5f44a67d25e1f7274d8b4568","610dee9f93bf0e000142e1e2","609fadf4e854490001de76e5","5fc7727657d5270001c13439","5ce50b0325e1f786708b4568","61357e64fc19ea000156b8f4","5cda279a716de103418b456f","67ad530e42b6a70031dcf677","5f979a6c25f67000014b78e8","6324648b99c2cc001ea5798a","6125b32898e9df0001ffb571","5d358b1625e1f7597e8b4568","5f617644288785c3188b4567","66a7458c1614f2002926802e","61612d407996b10001dae647","5bc5a951716de1a94f8b6fbc","60bf256eb80a970001e8eda7","66a7456c72a752003c29750f","5bc5a8a4716de1a94f8b6fba","5aefe31e846c2e5e7e8c9d56","5bc5a799716de1a94f8b6fb4","60ed081263c2ca00018a9a26","5d5295fa26c3d1af2f8b4568","5bc5a80a716de1a94f8b6fb7","5bc5a969716de1a94f8b6fbd","610b7e2cc4473b0001e2d938","61317f810b365a000119e979","60cc068812001400013558bb","5cda2044716de103418b456a","5c42f199d033a1e73c8b4567","60530e5d419018000179e356","5f59c7763ed1720001756953","66ea9f89d63482003c08fa59","5ccadf64716de1c6058b4573","65687a4fefd57e002000457f","5e7d70d825e1f7656a8b4576","5ccadf31716de1c6058b4571","614057a60b365a000119ee44","60e2bde433c053000197b338","5c98333ad033a1925b8b4572","5f45f262cd6a4e00012217d5","611c786b401bdd0001d1170e","611b9b1981ff03000136ac15","5ed5c66126c3d157358b456a","60cb0c0c13416b00010b1907","6115d5c15dc46f0001014595","5be3ad39716de100468c17cb","5ce35521716de1db018b4569","5b8d2252c0a1ea3a278b8b79","6097dfca236f4500011f99f0","6125b3c1873bd00001c32e3e","602e5b1c90549400017ce586","5f7fdae6ba782b0001f2cd59","67360c0e3f6602002cb11ec8","5cf089f626c3d12d278b4567","611e3d73b5871d00015f595b","66a3387b1614f2002926802b","66e0033dd63482003c08fa54","5a1e15c8916edfa0508f9a14","619712d306d44f0001cd1733","5c791264d033a197258b456e","5dde3e8d26c3d18c5c8b4569","5f1ea76f2a331e00010516d2","67592ab1ca4d37002a23d586","5be3add3716de100468c17cd","5fd6d3cd28d6b30001452823","5d52970626c3d1af2f8b456e","5f1ea6522a331e00010516d1","5f7fdb63cd6a4e00016c0624","612f02d00b365a000119e973","5f9798a025e1f700498b4568","5dcb9a3825e1f7987e8b456a","5f3a19b83ed17200017568cb","5e8c30d026c3d175768b456f","60c9a5bd12001400013557dc","66b5af634e68b5002a3f86e2","6753b3fec5dfdc003e8d5b94","6073bec99e81a000018cbbaf","611333a613d85c00011a63c1","6659c6d850e982002a4948f0","61ea47ce5f5f3f00018f9dc8","5f3be0d026c3d1b70a8b4567","5c6bccb9716de181098b456f","6759628e5543450028e2d8d4","6111e05381ff03000136abf4","5da6ee8925e1f74f278b4569","5cb96a27d033a172168b456a","5fb1eaa1be377b0001b042b7","5f1f860e25e1f7b8208b4567","5cbee03fd033a15b498b456b","6153d6fcb547af0001ddf091","5fc4a6b528878580748b4568","5cf0f95326c3d12d278b456b","61ea410e7bfef80001bc054c","5cc11147716de1c6058b4569","610f3faf5dc46f0001013309","5de87fbd26c3d12d538b4568","5f2ce57ebac21c0001deac52","666c2607d18325002b3ca7e5","5fa8d6cf25e1f7a04c8b456d","668f4b55447158002aed1d69","5bc5a7ba716de1a94f8b6fb5","5eb749e22887858c528b4573","5bf29008716de100468c6c9d","616d0a362149230001fc6368","6108f66781ff030001369967","5f990c32167c260001aa390b","632ae6ea2edafb001d9e6b5b","5d00a82725e1f7f5498b4576","5c3be4e0716de1416b8b456b","6577c137e5b03c001f0b1bc0","5ee9e782288785661f8b456e","6708d8cbd63482003c08fa65","5f56328a5f5f3f0001880624","5f3a19843ed17200017568ca","5fec1848c6d901000130637b","5bda6a57716de100468be811","5c1f043f716de13e768b4f15","5b9884bbc0a1ea8f1c8b5a6f","5d52964f26c3d1af2f8b456a","60e7b21425e1f70a768b4568","5dc2330526c3d171698b456b","5bbc901bd033a19c678bb026","5edde4ee26c3d12b1a8b4569","6704f15ed63482003c08fa63","5fb72baf1bdf020001f52dac","5ca4683ad033a1925b8b4585","5f99109e167c260001aa390c","5bf2907a716de100468c6ca3","605830d925ef1600012ed29a","5f1560c740ac940001899af2","53eb382d7f8b9ac3b18b4573","5f9f7fa13f34a90001c3bd4b","5cbedf89d033a15b498b4568","5f486c04cd6a4e00012217d6","5e7c992a2887853a598b4567","666426c2430c30003937d5e5","5f3a19513ed17200017568c9","6153d558ff642500016a6fb8","5d5663db25e1f7b0178b456e","5a1e1936916edfb8508fe232","6160fe377996b10001dae645","5f990cef10a344000184fcaf","5ecb353426c3d1cf748b4576","5f9f7f30f71bcd00018820a4","60b77a4126c3d1be578b4567","60ace4a89c78ce00019fab22","5c7de9c6716de1fc708b456a","5f5f09313ed1720001756968","5ff6a59710a3440001ff23e2","664fe06350e982002a4948e9","5c04bde4716de1403a8b679c","5c0f25b7716de1e77d8b4a71","5cbfd308d033a15b498b456e","5cda2241716de103418b456c","5f9914b481b78d0001549ae6","5c7de9f6716de1fc708b456c","60c9a63b2df0c70001523bf1","5aefe582c3c44297588d2230","5d7f517725e1f755198b456c","60c05f4691c66a0001ca382a","5cda275d716de103418b456d","6098f69a00352f000170d9ac","5f1f9a4b25e1f7b8208b4569","5fc8515c92d2d10001ef3292","5f7fd93f26c3d1714c8b4568","5ca46861d033a1925b8b4586","609fb13fcfac8e00017cdf14","5c3be4ab716de1416b8b4568","610f3fdb4448c70001e403b6","5b221e7ac5702ec65e8b8370","611b758425e1f74e288b4568","5fceefec6d1d940001c583e1","5dc2a0f626c3d171698b456c","65f3c84d3b43300043e1b4d3","612dd2cc7f908c00015a129b","60d6c24b33c053000197b1ff","5e7d6d7025e1f7656a8b456a","5da6b14625e1f74f278b4568","5cf100b426c3d12d278b4577","6065671915283000019db23d","5b50425cc0a1eac91e8b4bc0","6058543a25ef1600012ed29b","60937b72d57a2700016301f4","5d64c09426c3d1c52a8b4567","5ecb34e126c3d1cf748b4575","5c7911afd033a197258b4567","65bc806f4c5b51001fb59860","5bf28f1c716de100468c6c93","607531eb98c7930001b4953e","5cf08a6026c3d12d278b4569","666fdb1de93798002cb3111b","5f92746b8bff5b00019ff20d","5d907a7b25e1f7de748b456c","5e61e10e25e1f7845f8b4567","5dde3e7026c3d18c5c8b4568","5faa2dc591c66a0001b15145","5cb969e4d033a172168b4569","5ecb331526c3d1cf748b4572","64a3b5eb4ac362001e226c45","5fa241cd3f34a90001c3bd5f","6704f337d63482003c08fa64","611c797cb5871d00015f5952","61089ed4c4473b0001e2d937","60bae26ba0378d0001621542","5d9e92f226c3d137758b456c","612607ab105093000180a6e1","60bee9519c78ce00019fdccf","671cff8e7fc10f002bb6c6e2","5c8879a6716de1f6468b456b","5b98b0e7c0a1ea8f1c8b5dc6","617f80dad5fe620001198325","5dc0cff026c3d171698b4569","60af33535b62bf00014a1365","6119c9294448c70001e4165c","5cda287b716de103418b4570","60efaae725e1f70a768b456b","5b8d2317c0a1ea3a278b8b7d","65b8d2c34c5b51001fb5985e","5f9e6ed657597600010e7915","5cf0fc0826c3d12d278b456e","61010822b5871d00015f26d5","5cbee07cd033a15b498b456c","5c2f3b2d716de13e768b9cc0","60eba58729d9770001b25bb4","6161030c65cc3e0001297260","5f744ad9cd6a4e00016c0622","5d8869c725e1f72b2e8b456f","65a51471f26c00001f54fa89","5f8d0cee8e72da0001e47305","5bf28ff7716de100468c6c9c","60a1d25bcadfad00011ea884","5df8d91c26c3d198128b4568","5df8d8fd26c3d198128b4567","5eb7498f2887858c528b4572","61ea469fe8544900013d2989","5aefeb54c3c4428b588d3b20","6098f7a3d57a270001630805","5b8d2267c0a1ea3a278b8b7a","5d4a33e126c3d1bf738b4568","66d977621614f20029268043","5a1e1770916edf3f518f9a83","5cdcd669d033a1d13f8b456a","5eddebc126c3d12b1a8b456c","60d6c25333c053000197b200","5a4f5c64916edf8b738c421c","5f9f7e7c25e1f77f698b4567","5fa8ad6525e1f7a04c8b456b","6145a6f3cf336900018c7d3f","5fab6098e820e3000184712b","5fd6fe7013416b000143c5e4","55dd4a41916edf5c169422c6","5e7d711225e1f7656a8b4578","61c2bb8525e1f76a628b4567","5faa2f3657597600010e7aa9","5b8d22f8c0a1ea3a278b8b7c","5fa52eaf3f34a90001c3be3b","5fa8d81b3f34a90001c3be4d","610b7d96401bdd0001d1047f","5b1decd545cd4269678b800e","5c04bdf7716de1403a8b679d","61357c3bfc19ea000156b8f2","5ca1ea99d033a1925b8b4583","5ff7f1f880a9b900012feafc","5f45f1e45f5f3f00018805ce","5f8d0da88e72da0001e47306","5f0858fd8782cc000151d322","61ea43cfef235f000148e3cf","612dd394ed45f7000116355f","5cda277d716de103418b456e","5ef83edc288785ff608b456a","5c386795d033a128618b4568","6670f0f0e93798002cb3111c","5f45f2cf5f5f3f00018805cf","5f843812cd6a4e00016c0626","5e96bcb826c3d175768b4571","674acd590bdbd7002606b33f","5c0f25cb716de1e77d8b4a72","668f970b447158002aed1d6c","5ee210f528878543408b4567","5cceb096716de1c6058b457d","5c887948716de1f6468b4567","60e033f87bb5150001227d11","602e5ae191c66a000134f359","6184ef20c4de4b0001de1fc0","5c987e9dd033a1925b8b457c","61ea42fa7bfef80001bc054e","673fe8100bdbd7002606b333","5d00a6eb25e1f7f5498b456d","5ea4ed4025e1f7d71e8b4567","60eba5cb77536200012e22ee","6097d98a603eb200015de8a1","61566c3eff642500016a6fcc","5be3adbf716de100468c17cc","5f61761dcd6a4e00012217db","5cd65e59716de103418b4567","5d84721d25e1f72b2e8b456a","66e2b848d63482003c08fa56","6784f7c72391bb003cb8086c","5c1f0473716de13e768b4f16","5a1e1997916edfaa508fce1f","5f44a765cd6a4e00012217d2","5fa8ed4b25e1f7a04c8b456f","609fac5780a9b900018dbcc9","5c3be4f4716de1416b8b456c","5cb00a0dd033a1d82a8b4567","5ee9831b288785661f8b456d","5d5297de26c3d1af2f8b4574","5c6bcc80716de181098b456d","5f8972e364c4190001d7d4a2","61b96597ddfe0f00014e04b3","5d9e92a226c3d137758b4569","5b7fd9cac0a1ea3a278b4ea2","5f1ea85026c3d13f2f8b4569","5d00a7f625e1f7f5498b4574","5fa8d8973f34a90001c3be4f","605853d500352f0001ad62f1","5fd9a33c13416b000143c5ee","5e7c99682887853a598b456a","5d7f529625e1f755198b456e","60e13c082e175600013bdf34","5f9913ce10a344000184fcb2","5f0858df8782cc000151d321","5d52971a26c3d1af2f8b456f","6497a64b52bd09001d54b2fa","60e552e263c2ca00018a9a22","5ee982d6288785661f8b456b","60bee72ed899670001467f4d","65c320bf4c5b51001fb59861","5bf28f32716de100468c6c94","5c6bccd5716de181098b4570","5d00a7a525e1f7f5498b4571","5fbcda6717e2c40001f5694e","5dae5e2b25e1f7a4498b456a","5c987a6cd033a1925b8b4573","61c2c117bfe0d70001cd1dc3","5c2f3b41716de13e768b9cc1","56f35790916edf7940ad8588","658399d4efd57e0020004580","5d84726525e1f72b2e8b456c","5f9254a68bff5b00019ff20c","60fcf810401bdd0001d0e4ba","61c2bdcc9814100001fadfe0","66b5ea0c4e68b5002a3f86e3","5c987b64d033a1925b8b4578","5e7d6f3025e1f7656a8b456e","5d0cadf326c3d1811a8b4568","61ea4450e8544900013d2987","67b58b7be8ed93002ca9bd08","5cceb0d0716de1c6058b457f","5c2f3a53716de13e768b9ca2","5f0d8d4d8782cc000151d34e","60af315ba8167400013b36bd","5f5f06a43ed1720001756967","611f88d43ad68200016726cc","60dd67cc33c053000197b210","5b50465ac0a1eac91e8b4bc6","5ce354f4716de1db018b4568","60b7793ed899670001467f3b","66593d783b43300043e1b4e2","5fb26fdfbe377b0001b042ec","5f0d7c9726c3d13e758b4567","5e87e1f226c3d175768b456b","5e7d6fcd25e1f7656a8b4573","60bf08b7b80a970001e8e683","5f9799a90af5c200011ae983","5dae5ce225e1f7a4498b4569","5e7d6d3c25e1f7656a8b4568","5c6bcc2a716de181098b456a","61c2e4c51b069600019658dc","5e87de2d26c3d175768b4569","6724a5ce62d2dc0055d3aa1c","5c3be4cd716de1416b8b456a","60efaaa07bfef80001781ccf","5ce749c625e1f7d35e8b4569","61c2bf0974367100013b5cbc","5f9914612887854d4f8b4569","60582eea3ed17200011e0a63","613185cabfe0d700013a590d","5b988410c0a1ea8f1c8b5a69","5ce7760e25e1f7d35e8b456a","5faa001825e1f7a04c8b4570","60630e40f71bcd0001291197","5da6ef0825e1f74f278b456d","5b779e2e01a6eaaf048b9eba","5d1ab30625e1f78b588b456a","5f0d7c4c8782cc000151d34d","60fe1b3b401bdd0001d0e4c1","5fd6ff2213416b000143c5e5","5f08591e8782cc000151d323","5e7d6d5725e1f7656a8b4569","60630d7a3ed17200012dd86d","61089ed1fafdd600011d2b1e","674ac65e0bdbd7002606b33e","618886301bd9bd000199af26","5f858623cd6a4e00016c0627","5e7c99532887853a598b4569","67203d887fc10f002bb6c6e3","60331573ec9409000134f0ca","642e37c6b8a22d001e937324","617f7e2e8f589400010dc711","5bf28fa3716de100468c6c98","6100bfcb13d85c00011a315b","611b9b8fb5871d00015f594e","5b1f74dfc5702e0c6c8b560c","5eab96b42887858c528b4569","5ed5c62f26c3d157358b4568","5f99115110a344000184fcb1","60ca01be4229c700018bac75","5e9a5d8026c3d126688b456f","5cb96975d033a172168b4567","60751102337c8b0001ae960e","5bf29069716de100468c6ca2","5e991e2126c3d126688b456c","5b1f781545cd4230038d2617","5ce749a825e1f7d35e8b4568","5f1652ab83c4e900012e2edd","5fa398facd6a4e000142309d","5e991e0c26c3d126688b456b","6357a0455fbee7001ff3ff6a","6572ec50e5b03c001f0b1bbe","5d7f532a25e1f755198b4571","5fbcd96bf4ff2b00011d7637","5fa8dabe91c66a0001b15105","5eddec1f26c3d12b1a8b456f","668f867bb81169002b4e2737","5ff6ad921f3e26000101c30e","5ee2112128878543408b4569","61764777fec21c0001c3d7d0","5bbc90fbd033a19c678bb02b","6722ca6d62d2dc0055d3aa1b","5f1f865225e1f7b8208b4568","66713cf6d18325002b3ca7e7","5d7233f826c3d161348b456b","61b965f14d8aea00014e1d70","5bbc90e9d033a19c678bb02a","6110c52393bf0e000142e1e8","6736f5f53f6602002cb11ec9","5b6aa0bbc0a1ea333a8b70df","5f7448faba782b0001f2cd52","5f7445fecd6a4e00016c061f","611333b23ad6820001672683","5fc89d880456140001107573","5bda6a38716de100468be810","58ff17f2916edf48288b4815","6061421e3ed17200012dd86a","5f5716f05f5f3f000188062d","5fe01d5f26c3d12d488b4567","5d5297b926c3d1af2f8b4573","64f032ccc3e01c001feb6f61","5ca468cad033a1925b8b4589","61ea4624f7ed6a00015ed28f","5c6bcc09716de181098b4569","6139e637b54230000102c56a","5ec7a21c26c3d1cf748b456a","5c88b741716de1f6468b4571","61c2be159814100001fadfe1","607510372b99810001c95f03","618a245a2887858d218b4567","5ea9558325e1f71e518b4569","5df1b00225e1f7ce6d8b4568","5e7d70c525e1f7656a8b4575","65f3c85e3b43300043e1b4d4","5c3867bed033a128618b4569","5ccadfd8716de1c6058b4578","6128bc772fec040001c09336","5db6b81f26c3d1b50c8b4567","6530c7445a9680001fa0f445","60bf0703b80a970001e8e682","5fe6ddd228878583028b4567","5b8d2239c0a1ea3a278b8b78","5d3c6c9526c3d112108b4568","60e26d9f7bb5150001227d16","5cb969b2d033a172168b4568","66f274f7d63482003c08fa5b","61ea4157e8544900013d2986","5ec7a2a626c3d1cf748b4570","5c1f0378716de13e768b4f12","65d862eaefd57e0020004582","60dd252dbd8d8c0001eca0e0","60fcf7f7401bdd0001d0e4b9","616801fdd996f50001826144","5c04bcdb716de1403a8b6796","67244e5aea48f5003cf8a573","5e8c30a826c3d175768b456e","5e7d6f6925e1f7656a8b456f","5d9e928526c3d137758b4568","61c2c04f74367100013b5cbd","6698e0b8b81169002b4e2739","609faa9fcadfad00011ea881","5f5632af5f5f3f0001880625","5e7d6f7b25e1f7656a8b4570","615563b38be57600015d8493","5e9a5d6226c3d126688b456e","613182fec2c3e40001724bb4","5d900e1d25e1f7de748b456a","6119c8f513d85c00011a63d0","611f4343401bdd0001d1171b","610f3fd0b5871d00015f46aa","5fe9b0b33dd1230001bd59ef","5c983324d033a1925b8b4571","5bf28fb8716de100468c6c99","605c76d5744bef0001875525","669f773e447158002aed1d6d","5d7f52c825e1f755198b456f","672c1958ea48f5003cf8a577","612c93c3b544f000012ccb75","5de87f9226c3d12d538b4567","5f22a5a683c4e90001cd8ef6","5f5f027f3ed1720001756966","5ca1ea16d033a1925b8b4581","5cf1009126c3d12d278b4576","5a1e16ef916edfab509003e2","66713a1dd18325002b3ca7e6","5f22a48c2a331e0001051712","5f1ea70a83c4e90001cd8ef5","664d4e810533dc00420f7724","607974fe303cf30001821f85","60916217a0378d0001317ab9","5b504229c0a1eac91e8b4bbf","5fa8d64a57597600010e7a54","609fad5ccadfad00011ea882","616562a62149230001fc6355","60ca012626c3d1f95f8b4567","608007e36002ac0001590d7b","5b5043a4c0a1eac91e8b4bc3","5cc7ad3a716de1c6058b456d","60ac8f58b80a970001e8b4ea","5fae5b0425e1f7311e8b4567","5f96b1e625f67000014b78e7","5c04be59716de1403a8b67a2","672864f7ea48f5003cf8a575","672589a8ea48f5003cf8a574","5f1ea6d03f34a90001d0b0f6","6030b42691c66a000134f361","5ec7a26426c3d1cf748b456d","5c3be504716de1416b8b456d","5ce50ace25e1f786708b4567","5c42f337d033a1e73c8b456a","5f2780153f34a90001d0b13f","5e7d70b025e1f7656a8b4574","5f22a51925e1f7d0218b4567","615fb7082149230001fc6340","5fdabe5613416b000143c5f1","5bbc90a6d033a19c678bb027","61459fdeb80a97000171ea46","611ddc4713d85c00011a6400","658cea50e5b03c001f0b1bc2","5f92537825e1f700498b4567","5c88797d716de1f6468b4569","6721e77762d2dc0055d3aa1a","5da9573125e1f7a4498b4567","5f9273a725f67000014b78e6","5ca46894d033a1925b8b4587","5c7912ebd033a197258b4574","5c7912bed033a197258b4572","5fb72a4617e2c40001f565d8","5d52969426c3d1af2f8b456c","609162b0603eb200015de286","5d3c6cbd26c3d112108b4569","5dbfa45026c3d171698b4567","5d5661dc25e1f7b0178b456c","5fb72c3417e2c40001f565e2","5c2f3a81716de13e768b9ca4","611b9dc981ff03000136ac16","614adfc0578dd40001a0b84c","61384eaf3ad6820001794967","5b779dfc01a6eaaf048b9eb9","6669488332f8ad002b200084","5cbee09dd033a15b498b456d","611a57513ad68200016726ab","5c04be35716de1403a8b67a0","60d02ca913416b00010b199d","5b98847fc0a1ea8f1c8b5a6d","60d308e37bb5150001227c4c","5f50b319cd6a4e00012217d7","66fc0a51d63482003c08fa60","67285d9e62d2dc0055d3aa1d","5c0f2567716de1e77d8b4a6e","606bc15926c3d196518b4567","61089eeab5871d00015f4690","574bec82916edf6f5c8fd8fd","5f7d6956cd6a4e00016c0623","5fe30a56e854490001606422","6135797509e56900014af76d","60017dc9328f7f00016d45f4","5f59c7f8cd6a4e00012217d9","674730638b0527002d5b5589","5c6bcc64716de181098b456c","61459fb78e72da000176dabb","61ea419bef235f000148e3cd","60ef9dcbc7260c000128b5be","5ec7a1e826c3d1cf748b4568","5fe4018825e1f7c4568b4567","5ff69b4d1f3e26000101c30a","5c1f039f716de13e768b4f13","5e87e1d926c3d175768b456a","5ee2110b28878543408b4568","65a5134bf26c00001f54fa88","5cdcc495d033a1d13f8b4567","613852cbfc19ea000156b904","5e9e9ed128878574728b4569","60b827dfb80a970001e8e679","5fa8ebeb239fe40001fe4387","5c9832fcd033a1925b8b456f","5ea9572625e1f71e518b456a","5fcb95f6337c8b000187b065","5f858a3e6b51570001eee966","66568a6a50e982002a4948ec","5f5f09815f5f3f000188064a","5c7d3703d033a1c65c8b4567","5b988467c0a1ea8f1c8b5a6c","674acd498b0527002d5b558c","5f8d2c8f8e72da0001e47309","5ea64c8925e1f71e518b4567","5d7f52f625e1f755198b4570","613186dabfe0d700013a590e","5fa8d69791c66a0001b15100","610f3fba93bf0e000142e1e5","613ec0d50b365a000119ee3f","5bf28fe5716de100468c6c9b","66b09ef672a752003c297511","61cad5e30a08680001ba963f","5c2f3b54716de13e768b9cc2","5c42f356d033a1e73c8b456b","61725bd625e1f7c57c8b4567","610f3fab93bf0e000142e1e4","5c88b714716de1f6468b4570","5c1f03e3716de13e768b4f14","5d72348326c3d161348b456e","5f1ea7443f34a90001d0b0f7","61ea46ebe8544900013d298a","5c7de9ad716de1fc708b4569","5dc91d2425e1f7987e8b4568","5da6eeb225e1f74f278b456a","5fa8d5db91c66a0001b150ff","60a3b7e300352f000170d9be","5b1f72fec5702eb86b8b45bc","61404508bfe0d700013a5df3","6528fe3c4f80e5001f57d36b","5cb00a3ed033a1d82a8b4569","5f9a3b18d57a2700018b6d2b","65b77e81f26c00001f54fa8b","5cf0fc3226c3d12d278b456f","5bb84b7ed033a1bd418bfd75","5ce50b6025e1f786708b456a","5b988449c0a1ea8f1c8b5a6b","6668fd5632f8ad002b200083","5fa51b9725e1f7a04c8b4569","5ecb337326c3d1cf748b4573","6102967b28878555568b4567","61286413e78e560001b0e456","5ec7a24a26c3d1cf748b456c","5d9079ff25e1f7de748b456b","5ccadf91716de1c6058b4575","5eddeb6f26c3d12b1a8b456b","5ee9e7a2288785661f8b456f","5ccadf7c716de1c6058b4574","607cf358ed45f700010e13f6","60e260bf2e175600013bdf37","59e5b2f8936edf906a8dbc7d","61286314ac2081000108081a","646b3b62647eaa001de7b385","66e4ed0ad63482003c08fa57","5c79128fd033a197258b4570","57e1f988916edfe82698d309","5eb21ec92887858c528b456d","5bf28f8f716de100468c6c97","5d2d624725e1f74c378b456a","614c270ab80a97000171ef02","5d1de02b25e1f78b588b456c","5d78745825e1f755198b4567","665701693b43300043e1b4e1","5fcf4438fc334f0001d5dc48","67590daf89001c002bfb0744","674841a30bdbd7002606b33d","59e5c010916edfc66d8fc2ac","5eb21f2c2887858c528b4570","5cf0fd6526c3d12d278b4572","5b6aa0f3c0a1ea333a8b70e1","5c791220d033a197258b456b","5c887991716de1f6468b456a","60f7853dfb921b0001fbb72c","5d64c0da26c3d1c52a8b4569","66cc605672a752003c297524","61089ee8b5871d00015f468f","5c79127ad033a197258b456f","674035cf0bdbd7002606b334","6097e11426c3d1487d8b4568","60dd2946fc19ea00014d7e31","5efc979a26c3d1f10c8b4568","5d8a04d825e1f7de748b4568","5cbee00bd033a15b498b456a","61ea4275ef235f000148e3ce","5cceb05a716de1c6058b457b","5c887969716de1f6468b4568","5db6b85326c3d1b50c8b4569","67592acbca4d37002a23d587","6197136d0d3cab0001b439c9","5cdcd692d033a1d13f8b456b","5db6b86826c3d1b50c8b456a","5a4f5b37846c2ecb2e9139eb","61357f63c2c3e40001724bbf","5f5f06eacd6a4e00012217da","5f44a391cd6a4e00012217d1","5cac8aead033a1925b8b458a","5ff4170e64c41900012db8ff","5d72346426c3d161348b456d","60bf0844288785f1078b4567","5f1548de93e0310001d360a9","61357cd593bf0e0001f0cc5c","5f74498cba782b0001f2cd53","5a4f5bc3936edf37309480ea","5d64c0b326c3d1c52a8b4568","5f85899d6b51570001eee965","606ef0cf98c7930001b4953d","665451303b43300043e1b4e0","65b77e874c5b51001fb5985d","61527fdeff642500016a6fb5","5cac8b3ad033a1925b8b458b","663b1d4a0533dc00420f7722","6153d8759e3c190001caa0ec","610298355dc46f0001011351","5f1ea5ed3f34a90001d0b0f3","5fd460ba28d6b30001452822","67932aa242b6a70031dcf675","5f858ccccd6a4e00016c062a","5f16526e93e0310001d360ac","5c0f257f716de1e77d8b4a6f","5fceef0e25e1f726598b4568","53ed62977f8b9ac4b18b4bc4","59e5b4ca916edf3a6d8f1309","6617d01a0533dc00420f7721","5cdcd73bd033a1d13f8b456e","5d2e933026c3d1506a8b4567","61ea45285f5f3f00018f9dc6","5ff418c59467d400016cfc0c","61cad62d25e1f7f5278b4567","67ad530342b6a70031dcf676","5d6f57ac26c3d161348b4568","5dcb99b525e1f7987e8b4569","5d00cc1c25e1f7f5498b4577","5bf28f62716de100468c6c95","60937bda2d436c0001c66cd6","5f99070f2887854d4f8b4567","617cb691d5fe620001198324","617b59843f2f840001d75c33","5a1e1469916edfcf3e8ccf7d","5fceedf125e1f726598b4567","5b7fd9f7c0a1ea3a278b4ea3","5d1ab2c025e1f78b588b4568","5e87e20f26c3d175768b456c","614d37b3bdd59700015d1a16","603319ba25e1f729558b4567","61ea47275f5f3f00018f9dc7","63b3a7735585bc001d5a749d","611b766a1dfa340001216f6c","65421470e0fd3a0020936ec6","63082945673fd0001d9fb2d3","5f7fd9d86b51570001eee929","667a8921447158002aed1d66","5fa39bcd57597600010e795b","673461aa2a1b9a002b12d31a","60fe1b40fafdd600011d0b4b","5ebca66326c3d1fa2f8b4568","5d7874a725e1f755198b4569","5c88b7c7716de1f6468b4574","5fa8d7d391c66a0001b15102","5f1ea5752a331e00010516d0","5f744a67ba782b0001f2cd55","5f924eb292d2d10001fa2b94","5dde3e5726c3d18c5c8b4567","5c6bcbee716de181098b4568","5ff7c845e820e300017b911a","5f77197c6b51570001eee90d","5c7912d4d033a197258b4573","5db7d61325e1f773298b4569","6784ce9e2391bb003cb8086b","5f8426cc28878521718b4567","60bf03d2b80a970001e8e681","5f8d0c23bac21c0001186331","5cfdfe0e25e1f7f5498b456b","5b98b0cbc0a1ea8f1c8b5dc5","5da6eec625e1f74f278b456b","603c884c90549400017ce5bf","5c04bd8a716de1403a8b6798","6037107990549400017ce5b1","5e7d6f9225e1f7656a8b4571","5f44a4f53ed17200017568f3","5d00a7da25e1f7f5498b4573","5ce7768e25e1f7d35e8b456c","5d56215525e1f7b0178b4568","6136d67693bf0e0001f0cc70","60a8ee8fcd84ea0001b3abe0","5cf737a326c3d17c648b4567","616522a3b547af0001ddf092","60f7e37b93bf0e000142c1f1","675a9c8eca4d37002a23d588","662244d13b43300043e1b4da","5d00a74325e1f7f5498b4570","5f9a38373dd12300014d106b","5d00a7bf25e1f7f5498b4572","5b5ae26cc0a1eaef368b7833","6114964593bf0e000142f464","5ff69a7e3c521d0001ea64aa","59e5b5df936edf8b2e8f235b","5bbc927bd033a19c678bb02d","61289332bf77c2000156801e","5c791207d033a197258b456a","5c9de6a6d033a1925b8b457f","5eb21ef62887858c528b456e","5e12a2b726c3d198518b4568","5fa24108239fe40001fe4267","6110c5003ad682000167141c","5cbd50f4716de13b7c8b4567","5be3ad15716de100468c17c9","5f990fc310a344000184fcb0","5f1ea42e2a331e00010516cf","5fcf43b525e1f7957e8b4567","5f7718b2ba782b0001f2cd56","5d72349d26c3d161348b456f","5c04be48716de1403a8b67a1","5e991e3526c3d126688b456d","612608a4bf77c20001568010","5c3be515716de1416b8b456e","61527ffe2149230001fc632a","5f858aefcd6a4e00016c0629","618883beb1cf45000102ccda","61664cda65cc3e000129726f","611f4306c089180001fba37b","5f9272f992d2d10001fa2b96","5fa52f75239fe40001fe437c","5b1f5f8145cd42e84a8c6c6b","672c1962ea48f5003cf8a578","5c79124cd033a197258b456d","6391a53ef48a2f001d061856","5b19fb8306752ee25f8bca61","5c791237d033a197258b456c","611ddc495dc46f00010145ab","61089ef84448c70001e403a6","5ddb521125e1f7fc448b4567","5f79aa05ba782b0001f2cd58","5cb00a26d033a1d82a8b4568","60ac6379a0378d000161e3a3","611ddc4125e1f719568b4568","5eb21f452887858c528b4571","5fceef5f6d1d940001c583e0","5cceb0b3716de1c6058b457e","5be3ade2716de100468c17ce","6176217f573aca0001777c2a","5f1ea80d2a331e00010516d3","5dd7989926c3d1c53c8b4567","5d7f4fb425e1f755198b456a","6100c7fab7e3530001579864","5ca1e9fed033a1925b8b4580","5ef4c4d2288785ff608b4569","5f8e509364c4190001d7d4a3","61ea440e5f5f3f00018f9dc5","5f277fae2a331e0001051731","5fcef07a045614000197289a","5c3867ecd033a128618b456b","5f7717af26c3d1714c8b4567","5cceb03e716de1c6058b457a","613ebf7793bf0e0001f0d123","67865d802391bb003cb8086d","665992363b43300043e1b4e3","611c799c81ff03000136ac1c","5d3588af26c3d1c2528b4567","5c3be4bc716de1416b8b4569","5dae5e4725e1f7a4498b456b","6736f7582a1b9a002b12d31c","6729b17cea48f5003cf8a576","5f44a60a25e1f7274d8b4567","66fc0a43d63482003c08fa5f","5f59c7a73ed1720001756954","5f1f86303f34a90001d0b0f9","5bf29056716de100468c6ca1","61ea4660ef235f000148e3d1","6114e8e3fafdd600011d3db2","5f2ce3ebc0e3630001ba0e2b","6151a92b974b9700018ac4a5","61888783249a220001f2737b","5e86eab826c3d175768b4567","673ab8363f6602002cb11eca","5d56619925e1f7b0178b456b","5f2ce7f983c4e90001cd8ef9","5d2d61c525e1f74c378b4569","60f7e35613d85c00011a3134","6100c06481ff0300013679b9","5cf100d726c3d12d278b4578","5cbd513a716de13b7c8b4568","5e9e522b28878574728b4567","5d86c74625e1f72b2e8b456d","5f22a5792a331e0001051713","5f90f3348bff5b00019ff20a","6153d7a67996b10001dae625","65839bcbe5b03c001f0b1bc1","5de89c8d26c3d12d538b456a","6111e05081ff03000136abf3","673161d53f6602002cb11ec7","5a4f5c94916edf8a738c4c19","61a71b49bfc4ae0001c17c90","61ea43885c27c800015b7d4f","5f02867f25e1f7e4038b4567","5cc11126716de1c6058b4568","61e13f29b219110001a594fb","6114969c13d85c00011a63c4","5ecb27e726c3d1cf748b4571","5fa0c620239fe40001fe4255","60db57e0fc19ea00014d7e2c","5d7234b826c3d161348b4570","670f267d918f86002a5b49ae","5e6f686426c3d129488b4567","5cceb074716de1c6058b457c","607511392c7e8f0001f51599","5f7447d8cd6a4e00016c0621","66d57c3f1614f20029268041","65e18875ba7653002bb471fd","5eab96d32887858c528b456a","5fa39a3e57597600010e7958","60715b3e5c6ce000018ddb41","5cc5b079716de1c6058b456b","5fa8ade357597600010e7a52","611c797025e1f719568b4567","5c2f3ad7716de13e768b9cbc","60331a9a91c66a000134f36a","65602c784c5b51001fb5984e","60dd297463c2ca00013d93f7","603711c725e1f7f1388b4567","5bbc90bbd033a19c678bb028","5bc5a86b716de1a94f8b6fb9","6098f5cb80a9b900018dbcb0","6100c1615dc46f000101134b","5f44a7dccd6a4e00012217d3","5ffbb7a1e820e300017b911c","61ea45a7e8544900013d2988","5f16523f83c4e900012e2edc","6748419a0bdbd7002606b33c","5cfdfdac25e1f7f5498b456a","5f9796db92d2d10001fa2bbd","5c2f3aad716de13e768b9cba","5cf0fa0526c3d12d278b456c","662dad293b43300043e1b4dc","611cd49cb5871d00015f5956","61405ef8ef2b8b00010b4909","5b1f5dc2c5702e26218d45c3","60db582725e1f7595e8b4567","654233aa4103c3001f54d76d","610297f3fafdd600011d0b67","5b1f5c04c5702e26218d39db","5cbedfcfd033a15b498b4569","60db581433c053000197b20b","655ac8c85990d40032354be0","61c2be610a08680001ba9625","612dd6057f908c00015a129c","6135d73378af7c00010363e7","5edde49626c3d12b1a8b4567","61ea480e5c27c800015b7d50","61357a2bc2c3e40001724bbe","60bae07d107bfc0001ad30b5","60f0e87bfd87c1000172fc9b","61c2bf4d17e2c400010c5b6d","60efaac38cf3960001d3ff55","5c7de992716de1fc708b4568","5faa2ecb239fe40001fe43f8","5f9f801691c66a0001b14fc9","5dcbb62225e1f7987e8b456b","5d56615125e1f7b0178b456a","610a5584b5871d00015f4693","60ace35775474200016efe49","5b7fda1cc0a1ea3a278b4ea4","5eaaa0f42887858c528b4567","60b47b965b62bf00014a298b","6098f87acfac8e00017cdef8","5fc466b00456140001117996","5f50b3405f5f3f000188060d","5fa39b9b91c66a0001b15000","5c7de9dd716de1fc708b456b","5cf0fd2026c3d12d278b4571","5f99096a2887854d4f8b4568","610b4a612887853c728b4568","5f858974ba782b0001f2cd5a","5f9f806bcd6a4e000142309c","63a5218ece6338001ef23d0b","612dd485ae9c1500015798e1","66f741a1918f86002a5b49a8","5500fe37916edf8e728b5030","5d1ab2e025e1f78b588b4569","614d3789b6f41900010434ec","611c785d93bf0e000142f485","5f7719faba782b0001f2cd57","5c987ac7d033a1925b8b4575","614ac49fb2e84500019eb919","5cbd5229716de13b7c8b4569","5f1378ce93e0310001d3609e","5dae5cae25e1f7a4498b4568","64bb1e5049ba8e001dba28b0","66b1ce6e72a752003c297513","5d8471b325e1f72b2e8b4568","5a4f5cec936edf98198f48a5","60e26f812e175600013bdf38","65ba1e874c5b51001fb5985f","5eab966c2887858c528b4568","61ea45e8ef235f000148e3d0","60bf059a71345800018b0c6b","5ec7a27a26c3d1cf748b456e","5ec7a28f26c3d1cf748b456f","5ce50b2e25e1f786708b4569","5d8471d125e1f72b2e8b4569","5f13790725e1f78b238b4567","64feeee74654c7001feac627","61357ae609e56900014af76e","5cbedf3dd033a15b498b4567","61ea44ea7bfef80001bc0550","5ec7a20126c3d1cf748b4569","5f22a54e3f34a90001d0b11b","6550832a5990d40032354bdd","666426c932f8ad002b200082","5ec7a1d026c3d1cf748b4567","5fa399f126c3d1a45c8b4567","5c0f2438716de1e77d8b4a6b","674955398b0527002d5b558a","5ebca64d26c3d1fa2f8b4567","5bc5a780716de1a94f8b6fb3","668f8546447158002aed1d6a","5c2f3a99716de13e768b9cb9","6098f820cadfad00011ea86b","5fa0c572239fe40001fe4254","5ecb343826c3d1cf748b4574","5c88b773716de1f6468b4572","5fa24c94f71bcd00018820a5","5eb21ea22887858c528b456c","5d089ee026c3d1be5d8b4567","5b6aa08dc0a1ea333a8b70de","5dc38a4626c3d171698b456e","606bc0d6b13d4600013ae81c","6668fd4e430c30003937d5e6","5fd6cf73f3a1d700014c1a38","60bf07f0a0378d0001621549","5d52976726c3d1af2f8b4571","5f8589c1cd6a4e00016c0628","61357edbfc19ea000156b8f5","5ee982a8288785661f8b4569","5a1e1664916edf414f8f84f9","5cda28d6716de103418b4571","5c987e3ed033a1925b8b4579","61bad85464648b000112cb63","5da6eed925e1f74f278b456c","5b988497c0a1ea8f1c8b5a6e","66fc0a18918f86002a5b49a9","5ee98291288785661f8b4568","66dad506d63482003c08fa51","6572fac2e5b03c001f0b1bbf","5c386770d033a128618b4567","5fa52ddf57597600010e7a3b","5cb96a70d033a172168b456c","5fae53effa8c7d000122fc11","5b98b0acc0a1ea8f1c8b5dc4","5f155ec825e1f78b238b4568","5fa24c13239fe40001fe426b","5f9f80f425e1f77f698b4568","64d9f97b929121001f646db8","61ea434c5f5f3f00018f9dc4","60b1ec9771345800018ae2dd","60751ad57003f20001ebd43d","606eedd92b99810001c95efa","5d00a70e25e1f7f5498b456e","60585ff5c6d901000166d7eb","5ccadf4b716de1c6058b4572","66b314fa02d240002b530255","5ed5c5bb26c3d157358b4567","60ace30eb80a970001e8b4ec","5d2805cb26c3d1740e8b4567","614c26f9cf336900018c7d40","5ce7764625e1f7d35e8b456b","5edde4ad26c3d12b1a8b4568","62ecf51a83c418001e68d656","5aefcef445cd429a0b8be1d7","5f92499b8bff5b00019ff20b","5aefe6c2c3c4420a208c2b7c","5fd34c7b25e1f77c038b4568","5ce50c6b25e1f786708b456b","658b9375efd57e0020004581","5dae5f6325e1f7a4498b456c","5cf4b6b926c3d12d278b457a","60e55f1933c0530001095674","5cf1006f26c3d12d278b4575","5fbcda0c642a1e0001f114d1","5c04bd75716de1403a8b6797","612c9487b544f000012ccb76","5b8d232ec0a1ea3a278b8b7e","60b7781f71345800018b0c62","5ef4c4a9288785ff608b4568","5d5295bb26c3d1af2f8b4567","5f5f095326c3d1fb318b4568","60fe022e5dc46f0001011342","5c987b35d033a1925b8b4577","6659796c50e982002a4948ef","60f7e3363ad682000166f424","60b8282a107bfc0001ad30b4","59e5bfb6916edf4f0c8c979f","5faa2e4557597600010e7aa8","5d3c6c7626c3d112108b4567","5b8d2343c0a1ea3a278b8b7f","5b779e6d01a6eaaf048b9ebc","62df910b1d06a1001ed62c4e","6037116f90549400017ce5b2","5cf08a3c26c3d12d278b4568","5db7d5d625e1f773298b4568","5cf1003b26c3d12d278b4574","5b779e4f01a6eaaf048b9ebb","5ccadefb716de1c6058b456f","609fa9ea2657b20001140687","58c4cdaa936edffe313e00fe","5c04bcbf716de1403a8b6795","5b9883eac0a1ea8f1c8b5a68","5c9832bdd033a1925b8b456c","5c2f3b14716de13e768b9cbf","66e2c782918f86002a5b49a2","5f44a79e3ed17200017568f6","5d1ab28325e1f78b588b4567","609160c12d436c0001c66cd4","5be3aacf716de100468c17c5","5e689ea725e1f711298b4569","5aefce8245cd4266028b5c94","6088d725236f4500011f8950","5ee9827b288785661f8b4567","5db6b83826c3d1b50c8b4568","5cdcd6f3d033a1d13f8b456d","5eb21f112887858c528b456f","60c2fcd63cdb4a000123d52e","66aa4a341614f20029268030","5d1ebab825e1f78b588b456d","5f5f08f926c3d1fb318b4567","5f99110b81b78d0001549ae4","5bc5a7d4716de1a94f8b6fb6","5d2d616825e1f74c378b4567","5f74470acd6a4e00016c0620","60dffd9244f7e0000191aead","5bd67da3716de100468bcc1a","5dde3ea226c3d18c5c8b456a","5f44a73225e1f7274d8b4569","5a4f5b8f846c2eb9108f484f","6067db235e71e00001433abc","5eddec0226c3d12b1a8b456e","5fe01bc23dd1230001bd510c","619b59c99629990001757605","60323fd92b99810001263588","5e87e23026c3d175768b456d","611c79a5401bdd0001d1170f","6163adea744bef0001377f40");

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private TagConstants tagConstants;

    @Autowired
    private TagVersionService tagVersionService;

    @Resource(name = RedisCacheConfig4Api.TAG_REDIS_CLIENT_UTIL_4API)
    private TagRedisUtil tagRedisUtil4Api;

    @Autowired
    private RuleProductsByStationDaoProxy ruleProductsByStationDaoProxy;

    @Resource
    private RuleSettingDao ruleSettingDao;

    @Resource
    private AdminOperationLogDao adminOperationLogDao;

    @Autowired
    protected RuleUsersDao ruleUsersDao;

    @XxlJob(value = "BackwardCompareJob")
    public ReturnT<String> execute(String param) throws Exception {
        try {
            try {
                log.info("BackwardCompareJob.execute.start; param={}", param);
                BackwardCompareJobHandler.CompareJobParams params = JsonUtil.jsonToBean(param, BackwardCompareJobHandler.CompareJobParams.class);
                if (Objects.isNull(params)) {
                    log.warn("BackwardCompareJob: params.invalid;param={}; ", param);
                    return ReturnT.FAIL;
                }
                Set<Long> ruleIds = params.getRuleIds();
                Set<String> stationIds = params.getStationIds();
                log.info("BackwardCompareJob.execute.start;params.ruleIds={},stationIds={}", ruleIds, stationIds);

                // 查出全量生效中的商品分仓策略
                Set<Long> usingRules = getUsingStationRuleIds();
                if (CollectionUtils.isEmpty(usingRules)) {
                    log.warn("BackwardCompareJob: usingStationRuleIds.empty;param={}; ", param);
                    return ReturnT.FAIL;
                }
                log.info("BackwardCompareJob.execute.start;usingRules={}", usingRules);

                // 确定比对策略（过滤要比对的生效中策略）
                List<Long> finalRuleIds = usingRules.stream()
                        .filter(r -> CollectionUtils.isEmpty(ruleIds) || ruleIds.contains(r))
                        .collect(Collectors.toList());
                log.info("BackwardCompareJob.execute.start;comparing ruleIds={}", finalRuleIds);

                // 查询最新正向结果版本
                List<RuleUsers> ruleUsersList = ruleUsersDao.queryLatestRuleUsersByRuleIdsAndDate(Lists.newArrayList(finalRuleIds), null);
                Map<Long, Long> ruleResultMap = Maps.newHashMapWithExpectedSize(ruleUsersList.size());
                ruleUsersList.forEach(ruleUsers -> ruleResultMap.put(ruleUsers.getRuleId(), ruleUsers.getId()));
                if (MapUtils.isEmpty(ruleResultMap)) {
                    log.warn("BackwardCompareJob: ruleResultMap.empty;param={}; ", param);
                    return ReturnT.FAIL;
                }
                log.info("BackwardCompareJob.execute.start;comparing ruleResultMap={}", ruleResultMap);

                // 确定比对站点
                stationIds = CollectionUtils.isEmpty(stationIds) ? allStationIds : stationIds;
                Set<String> finalStationIds = stationIds;
                log.info("BackwardCompareJob.execute.start;comparing finalStationIds={}", finalStationIds);

                // 按站点比对全量策略 max = 1000
                Set<String> usingStationRuleIds = finalRuleIds.stream()
                        .map(String::valueOf)
                        .collect(Collectors.toSet());
                log.info("BackwardCompareJob.execute.start;final comparing ruleIds={}", usingStationRuleIds);

                // 清理上次比对数据
                Set<String> lastCompareFailRules = tagRedisUtil4Api.getAllMembers("tag:compare:back:fail.rules");
                log.info("BackwardCompareJob.execute.start;last compare fail ruleIds={}", lastCompareFailRules);
                tagRedisUtil4Api.remove("tag:compare:back:fail.rules");

                MetricsUtils.logTransactionWithoutResult("backward_compare_running",
                        "all_station_" + stationIds.size() + ",rule_" + usingStationRuleIds.size(), "",
                        () -> startCompare(finalStationIds, usingStationRuleIds, ruleResultMap, usingRules));
            } catch (Exception e) {
                log.error("BackwardCompareJob.execute.exception; param={}; ", param, e);
                return ReturnT.FAIL;
            }
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("BackwardCompareJob.execute.exception;param={}; ", param, e);
            return ReturnT.FAIL;
        }
    }

    private void startCompare(Set<String> stationIds, Set<String> usingStationRuleIds, Map<Long, Long> ruleResultMap, Set<Long> usingRules) {
        for (String stationId : stationIds) {
            CompletableFuture.runAsync(() -> MetricsUtils.logTransactionWithoutResult("backward_compare_running",
                    "single_station", "station_id:" + stationId,
                    () -> doCompareSingleStation(stationId, usingStationRuleIds, ruleResultMap, usingRules)));
        }
    }

    private void doCompareSingleStation(String stationId, Set<String> usingStationRuleIds, Map<Long, Long> ruleResultMap, Set<Long> usingRules) {
        // 线程安全的Multimap结构
        SetMultimap<String, String> stationRuleDiffMap = Multimaps.synchronizedSetMultimap(HashMultimap.create());
        // 遍历策略 max = 1000
        for (String ruleId : usingStationRuleIds) {
            CompletableFuture.runAsync(() -> MetricsUtils.logTransactionWithoutResult("backward_compare_running",
                    "single_station_rule", "station_id:" + stationId + ",rule_id:" + ruleId,
                    () -> doCompareSingleRule(stationId, ruleResultMap, usingRules, ruleId, stationRuleDiffMap)));
        }
        MetricsUtils.logEventWithSpan("backward_compare_station",
                "exist_diff_" + (stationRuleDiffMap.isEmpty() ? "no" : "yes"),
                "station:" + stationId + ",diff-size:" + stationRuleDiffMap.size());
    }

    private void doCompareSingleRule(String stationId, Map<Long, Long> ruleResultMap, Set<Long> usingRules, String ruleId, SetMultimap<String, String> stationRuleDiffMap) {
        // 查询最新正向数据（MySQL）
        Long preResultId = ruleResultMap.get(Long.valueOf(ruleId));
        Map<String, Set<String>> latestStationIdProductIdsMap = queryResultByResultId(preResultId, Long.valueOf(ruleId));
        log.info("BackwardCompareJob.execute.start;single-comparing;stationId={},ruleId={},preResultId={},forward-size={}",
                stationId, ruleId, preResultId, latestStationIdProductIdsMap.size());
        // 查询逆向数据（Redis）
        Set<String> existProductIds = getProductIdsFromRedisByStation(stationId);
        if (CollectionUtils.isEmpty(existProductIds)) {
            MetricsUtils.logEventWithSpan("backward_compare_station_rule", "redis_empty", "rule:" + ruleId + ",station:" + stationId);
            return;
        }
        List<Object> fields = new ArrayList<>(existProductIds);
        List<Object> results = queryLatestBackwardResultByStationId(fields, stationId);
        log.info("BackwardCompareJob.execute.start;single-comparing;stationId={},ruleId={},redis-size={}", stationId, ruleId, results.size());
        // 遍历指定站点下商品列表 开启单个比对
        AtomicInteger diffSize = doSingleCompare(usingRules, latestStationIdProductIdsMap, stationId, ruleId, fields, results);
        String diffInfo = "no";
        if (diffSize.get() > 0) {
            diffInfo = "yes";
            // 直接添加无需锁
            stationRuleDiffMap.put(stationId, ruleId);
        }
        MetricsUtils.logEventWithSpan("backward_compare_station_rule", "exist_diff_" + diffInfo, "rule:" + ruleId + ",station:" + stationId);
        log.info("BackwardCompareJob.execute.start;single-comparing;end;exist-diff={},station={},rule={}",diffInfo , stationId, ruleId);
    }

    private AtomicInteger doSingleCompare(Set<Long> usingRules,
                                          Map<String, Set<String>> latestStationIdProductIdsMap,
                                          String stationId,
                                          String ruleId,
                                          List<Object> fields,
                                          List<Object> results) {
        AtomicInteger diffSize = new AtomicInteger(0);
        for (int i = 0; i < fields.size(); i++) {
            String field = (String) fields.get(i); // 商品ID(Redis)
            String value = (String) results.get(i); // 命中策略(redis)
            MetricsUtils.logEventWithSpan("backward_compare_inner", "Total", "");
            Set<String> existLatestBackwardRuleIds;
            try {
                existLatestBackwardRuleIds = new HashSet<>(Arrays.asList(value.split(Constants.COMMA)));
            } catch (Exception e) {
                log.error("BackwardCompareJob: forwardMapping.rule:{},station:{},product:{},exist_rule:{}", ruleId, stationId, field, value, e);
                continue;
            }
            // 从全量数据中每个站品角度去比对，向指定策略最新正向结果集进行比对，发现异常。
            String currRuleId = String.valueOf(ruleId);
            log.info("BackwardCompareJob.execute.start;single-comparing;station={},product={},rule={}", stationId, field, currRuleId);
            Set<String> latestMysqlProductIds = latestStationIdProductIdsMap.get(stationId);
            if (CollectionUtils.isEmpty(latestMysqlProductIds) && CollectionUtils.isEmpty(existLatestBackwardRuleIds)) {
                MetricsUtils.logEventWithSpan("backward_compare_inner", "Success_all_empty", "rule:" + ruleId + ",station:" + stationId + ",product:" + field);
                log.warn("BackwardCompareJob.execute.start;single-comparing;success;station={},product={},rule={}", stationId, field, currRuleId);
                continue;
            }
            // 过滤生效中的策略
            Set<String> usingExistLatestBackwardRuleIds = existLatestBackwardRuleIds.stream()
                    .filter(r -> StringUtils.isNotBlank(r) && usingRules.contains(Long.valueOf(r)))
                    .collect(Collectors.toSet());
            log.info("BackwardCompareJob.execute.start;single-comparing;station={},product={},rule={},redis-all:{},redis-using:{}", stationId, field, currRuleId, existLatestBackwardRuleIds, usingExistLatestBackwardRuleIds);
            boolean currRuleIdExistInRedis = false;
            boolean currRuleIdExistInMysql = false;
            if (!CollectionUtils.isEmpty(usingExistLatestBackwardRuleIds) && usingExistLatestBackwardRuleIds.contains(currRuleId)) {
                currRuleIdExistInRedis = true;
            }
            if (!CollectionUtils.isEmpty(latestMysqlProductIds) && latestMysqlProductIds.contains(field)) {
                currRuleIdExistInMysql = true;
            }
            if (currRuleIdExistInRedis != currRuleIdExistInMysql) {
                MetricsUtils.logEventWithSpan("backward_compare_inner", "Fail,Redis:" + currRuleIdExistInRedis + ",Mysql:" + currRuleIdExistInMysql,
                        "rule:" + ruleId + ",station:" + stationId + ",product:" + field);
                log.error("BackwardCompareJob.execute.start;single-comparing;fail;station={},product={},rule={},redis-mysql:{}-{},redis:{},mysql:{}",
                        stationId, field, currRuleId, currRuleIdExistInRedis, currRuleIdExistInMysql, value, latestMysqlProductIds);
                diffSize.addAndGet(1);
                tagRedisUtil4Api.putSingleSet("tag:compare:back:fail.rules", ruleId, 86400);
                doRepair(stationId, field, currRuleId, currRuleIdExistInRedis, currRuleIdExistInMysql, usingExistLatestBackwardRuleIds);
            } else {
                MetricsUtils.logEventWithSpan("backward_compare_inner", "Success", "");
            }
        }
        return diffSize;
    }

    private void doRepair(String stationId, String field, String currRuleId, boolean currRuleIdExistInRedis, boolean currRuleIdExistInMysql, Set<String> usingExistLatestBackwardRuleIds) {
        if (!tagConstants.isRuleBackCheckRepairSwitch()) {
            MetricsUtils.logEventWithSpan("backward_compare_inner_repair", "switch_off", "currRuleId:" + currRuleId + ",station:" + stationId + ",product:" + field);
            return;
        }
        // 待修复redis逆向key
        if (currRuleIdExistInRedis && !currRuleIdExistInMysql) {
            // 删除 redis 中的数据
            if (!CollectionUtils.isEmpty(usingExistLatestBackwardRuleIds)) {
                usingExistLatestBackwardRuleIds.remove(currRuleId);
            }
            MetricsUtils.logEventWithSpan("backward_compare_inner_repair", "repairing_remove", "currRuleId:" + currRuleId + ",station:" + stationId + ",product:" + field);
        } else if (!currRuleIdExistInRedis && currRuleIdExistInMysql) {
            // 添加 redis 中的数据
            usingExistLatestBackwardRuleIds.add(currRuleId);
            MetricsUtils.logEventWithSpan("backward_compare_inner_repair", "repairing_append", "currRuleId:" + currRuleId + ",station:" + stationId + ",product:" + field);
        }
        doCoverProductRulesResult(stationId, field, usingExistLatestBackwardRuleIds);
    }

    private void doCoverProductRulesResult(String stationId, String productId, Set<String> coveringBackwardRuleIds) {
        try {
            String cacheKey = CacheKeyConstants.getProductStationBackwardHashResultKey(stationId);
            String latestBackwardRuleIdsStr = String.join(Constants.COMMA, coveringBackwardRuleIds);
            // 执行覆盖
            tagRedisUtil4Api.getiCacheManager().getStringRedisTemplate().opsForHash().put(cacheKey, productId, latestBackwardRuleIdsStr);
            // 过期时间 默认：7天
            tagRedisUtil4Api.getiCacheManager().getStringRedisTemplate().expire(cacheKey,
                    tagConstants.getProductStationBackwardResultExpireDays(), TimeUnit.DAYS);
            MetricsUtils.logEventWithSpan("backward_compare_repair", "fixed", "stationId:" + stationId + ",productId:" + productId + ",coveringBackwardRuleIds:" + coveringBackwardRuleIds);
            log.info("BackwardCompareJob: forwardMapping.comparing.fix.station={}.product:{}.coveringBackwardRuleIds:{}", stationId, productId, coveringBackwardRuleIds);
        } catch (Exception e) {
            log.error("BackwardCompareJob.execute.end;error, stationId:{}", stationId, e);
        }

    }

    /**
     * 根据 stationId 获取该 station 在 Redis 中所有的 productMongoId（即 hash 的所有 field）
     */
    private Set<String> getProductIdsFromRedisByStation(String stationId) {
        Set<String> productIds = new HashSet<>();
        String key = CacheKeyConstants.getProductStationBackwardHashResultKey(stationId);
        try {
            RedisTemplate<String, String> redisTemplate = tagRedisUtil4Api.getiCacheManager().getStringRedisTemplate();
            Set<Object> fields = redisTemplate.opsForHash().keys(key);
            if (fields != null) {
                for (Object field : fields) {
                    productIds.add(field.toString());
                }
            }
        } catch (Exception e) {
            log.error("Error fetching product IDs from Redis for stationId: {}", stationId, e);
        }
        return productIds;
    }

    private List<Object> queryLatestBackwardResultByStationId(List<Object> fields, String stationId) {
        if (CollectionUtils.isEmpty(fields)) {
            return Collections.emptyList();
        }
        try {
            String cacheKey = CacheKeyConstants.getProductStationBackwardHashResultKey(stationId);
            return tagRedisUtil4Api.getiCacheManager().getStringRedisTemplate().opsForHash().multiGet(cacheKey, fields);
        } catch (Exception e) {
            log.error("# extra deal,query latest backward result, error, stationId:{}", stationId, e);
        }
        return Collections.emptyList();
    }

    private Map<String, Set<String>> queryResultByResultId(Long resultId, Long ruleId) {
        long startTime = System.currentTimeMillis();
        if (Objects.isNull(resultId) || Objects.isNull(ruleId)) {
            return Collections.emptyMap();
        }
        try {
            Map<String, Set<String>> stationIdProductIdsMap = new HashMap<>();
            CodeUtil.waterfallProcess(new WaterfallProcessInterface<RuleProductsByStationDetail, String>() {
                @Override
                public List<RuleProductsByStationDetail> getPage(String sortParam, int size) {
                    return ruleProductsByStationDaoProxy.queryByRuleIdAsc(ruleId, sortParam, resultId.intValue(), size);
                }
                @Override
                public void process(List<RuleProductsByStationDetail> pageList) {
                    pageList.forEach(detail -> {
                        String productMongoId = detail.getProductMongoId();
                        List<String> stationIds = Arrays.asList(detail.getStationIds().split(Constants.COMMA));
                        if (StringUtils.isBlank(productMongoId) || CollectionUtils.isEmpty(stationIds)) {
                            return;
                        }
                        stationIds.forEach(stationId -> {
                            Set<String> productIdSet = stationIdProductIdsMap.getOrDefault(stationId, new HashSet<>());
                            productIdSet.add(productMongoId);
                            stationIdProductIdsMap.put(stationId, productIdSet);
                        });
                    });
                }

                @Override
                public int getSize() {
                    return Constants.DEFAULT_USER_GROUP_COUNT;
                }

                @Override
                public String getNextParam(RuleProductsByStationDetail last) {
                    return last.getId();
                }

                @Override
                public String getFirstParam() {
                    return null;
                }
            });
            log.info("# extra deal, query result by result id, end, stationIdProductIdsMap:{}",
                    objectMapper.writeValueAsString(stationIdProductIdsMap));
            boolean isEmpty = MapUtils.isEmpty(stationIdProductIdsMap);
            MetricsUtils.logTimer("rule:" + ruleId + ",result:" + resultId,
                    "res_size:" + (isEmpty ? "0" : stationIdProductIdsMap.size()),
                    !isEmpty,
                    startTime,
                    "queryResultByResultId");
            return stationIdProductIdsMap;
        } catch (Exception e) {
            log.error("# extra deal, rule exec finish deal extra result consume error, queryLatestResult, ruleId:{}, resultId:{}",
                    ruleId, resultId, e);
            return Collections.emptyMap();
        }

    }

    private Set<Long> getUsingStationRuleIds() {
        RuleSetting ruleQuery = new RuleSetting();
        ruleQuery.setCategoryType(CategoryEnum.PRODUCT.getValue());
        ruleQuery.setIsValid(ValidEnum.VALID_YES.getValue());
        ruleQuery.setRuleStatus(RuleStatusEnum.USING.getValue());
        ruleQuery.setScene(RuleSceneEnum.NEW_MARK_BYSTATION.getScene());
        ruleQuery.setSelectType(RuleSettingSelectTypeEnum.SELECT_TYPE_BY_TAG.getValue());
        ruleQuery.setUpdateType(RuleSettingUpdateTypeEnum.UPDATETYPE_ROUTINE.getValue());
        List<RuleSetting> ruleSettings = ruleSettingDao.selectList(ruleQuery);
        Set<Long> usingStationRuleIds = ruleSettings.stream().map(RuleSetting::getId).collect(Collectors.toSet());
        log.info("BackwardCompareJob: forwardMapping.usingStationRuleIdsSize={},usingStationRuleIds={}", usingStationRuleIds.size(), usingStationRuleIds);
        return usingStationRuleIds;
    }

    @Data
    public static class CompareJobParams {
        private Set<Long> ruleIds;
        private Set<String> stationIds;
    }

}
